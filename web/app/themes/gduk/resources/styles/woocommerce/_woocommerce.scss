@import "cart";
@import "checkout";
@import "product-tables";
@import "single-product";

.woocommerce-info{
  &::before{
    color: #000;
  }

  border-top-color: $primary;
}

.woocommerce ul.products li.product .woocommerce-loop-product__title,
.single-product-link {
  font-size: 15px;
}

.single-product-link {
  font-weight: bold;
}

section.upsells ul.products {
  display: flex;
  flex-direction: row;
}

section.upsells ul.products li.product {
  display: flex;
  flex-direction: column;
  float: none;
}

section.upsells ul.products li.product .button.wc-quick-view-button {
  margin-top: auto !important;
}
section.upsells ul.products li.product .button {
  margin-top: 1rem;
  width: fit-content;
}

section.upsells ul.products,
section.upsells ul.products li.product {
  margin-bottom: 0;
}

.woocommerce form .form-row .input-text,
.woocommerce-page form .form-row .input-text {
  padding: 10px;
}

.wpforms-container.wpf-center {
  margin: 0 auto !important;
  /* Adjust the width in the next 2 lines as your site needs */
  max-width: 600px !important;
  width: 600px !important;
}

// Hide registration prompt when logged in
body.woocommerce-shop.logged-in #main > #fullwidth {
  display: none
}