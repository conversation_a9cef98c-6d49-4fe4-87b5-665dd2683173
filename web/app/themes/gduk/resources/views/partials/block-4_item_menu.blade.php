@php
  $products = get_products_by_term(get_queried_object());
  $colors = ['bg-primary', 'bg-black text-white', 'bg-white', 'bg-primary', 'bg-black text-white', 'bg-white', 'bg-primary']
@endphp

<section id="four-item-menu" class="bg-secondary py-5">

  <div class="container">
    <div class="row">

      @foreach ($block['items'] as $item)

        <div class="col-12 col-md-3 my-3">
          <div class="{{ $colors[$loop->index] }} d-flex flex-column h-100">
            <div class="">
              @if ($item['image'])
                <a href="{{ $item['link']['url'] }}">
                  <img src="{{ $item['image']['url'] }}" class="w-100">
                </a>
              @endif
            </div>
            <div class="flex-grow-1">
              <div class="d-flex flex-column justify-content-between h-100 p-4 pt-5">
                <div class="pb-3">
                  <a class="text-dark text-decoration-underline-none" href="{{ $item['link']['url'] }}">
                    <h5 class="text-uppercase">{!! $item['title'] !!}</h5>
                  </a>
                  <hr class="thick-black">
                  <span>{!! nl2br($item['text']) !!}</span>
                </div>
                @if($item['link']['title'])
                  <div class="d-flex justify-content-between align-items-end">
                    <a class="text-decoration-underline text-dark" href="{{ $item['link']['url'] }}">{{ $item['link']['title'] ?? 'Find out more' }}</a>
                  </div>
                @endif
              </div>
            </div>
          </div>
        </div>

      @endforeach

    </div>
  </div>

</section>
