=== Product Export for WooCommerce to CSV, Excel, XML, and the Google Merchant Center ===
Contributors: soflyy, wpallimport
Tags: product export, export products, woocommerce product export, export woocommerce products, CSV export
Requires at least: 5.0
Tested up to: 6.8
License: GPLv2 or later
Requires PHP: 7.4
Stable tag: 1.0.3

Export WooCommerce products to CSV, Excel, or XML files, as well as to the Google Merchant Center. Run your WooCommerce product export on demand or on a schedule.

== Description ==

Export products to CSV, Excel, Google Sheets, or XML files with the WooCommerce Product Export Add-On for [WP All Export](https://wordpress.org/plugins/wp-all-export/).

Select your product export columns, then rearrange, combine, or rename them as needed. Add any columns, including those created by 3rd-party plugins or add-ons. You can even create custom columns by using our embedded functions.

Apply flexible filters to limit your export to the exact products that you want.

Bulk edit your products or migrate them from one site to another with just a few clicks using a combination of WP All Export and [WP All Import](https://wordpress.org/plugins/wp-all-import/).

Schedule your product export to run automatically, then use Zapier to integrate your export file with any of 1000s of external applications.

Need to upload your product data to the Google Merchant Center? No problem. We’ve created a special template that will help you generate a Google Shopping feed in a fraction of the time it would take to do it on your own.

**[Click here to try WP All Export right now](https://www.wpallimport.com/try-export-free/).**

[youtube https://www.youtube.com/watch?v=adAe-UYS58A/]

= Select Your WooCommerce Product Export Columns =

As the video demonstrates, you can use our Drag & Drop tool to quickly select your product export columns. You can rearrange these columns, rename them, and even combine them. If you need an entirely custom product field, you can build it using our embedded PHP functions.

In other words, you can build a product export that meets your exact layout requirements, whatever they may be.

= Create Flexible Product Export Filters =

Sometimes, you need to export only a subset of your products. For example, you may want to export only active products.

You can do this easily with WP All Export. You can build filters using any combination of product fields, including those created by 3rd-party plugins or add-ons.

You can build simple filters, combination filters, or nest your filter conditions (equivalent to using parentheses).

If filtering against date fields, you can even use relative date filter conditions like “last week” or “last month”.

There is no need to compromise here. You can filter your product export in whatever way you want.

= Export Products to CSV, Excel, and XML =

By default, our plugin exports products to a CSV file format.

But you can just as easily export products to Excel using either the .XLS or .XLSX formats.

If you prefer XML, you can build everything from the simplest to the most complex XML feed imaginable.

You can also easily export your product data to Google Sheets for collaborative editing and analysis. 

= Export Products to the Google Merchant Center =

The Google Merchant Center (GMC) is a platform that lets you share your product information across Google, which makes it easier for customers to find your products and your store.

That’s the good news. The bad news is that GMC has extensive data requirements that can take many hours to satisfy.

To address this, we’ve built a product export template that helps you comply with Google’s requirements. Just choose the Google Merchant Center Product Feed as your export type, fill out the onscreen forms, and you’ll be able to upload your products in a matter of minutes.

Even better, if Google modifies its requirements in any way, we immediately update our plugin to match, meaning you can just re-run your product export to fulfill the new requirements.

= Bulk Edit Your WooCommerce Product Data =

Bulk editing WooCommerce products is a common task. Sometimes, you need to alter many product prices or modify product descriptions as part of a sales promotion.

Whatever your reason, we make bulk editing products painless. Just export your products, edit them in a spreadsheet like Excel or Google Sheets using features like search-and-replace, copy-and-paste, etc., then import everything back into WooCommerce using [WP All Import](https://wordpress.org/plugins/wp-all-import/).

What makes this so painless is that WP All Import is aware of the export settings, which allows it to automatically configure the import process. No muss, no fuss, just a quick, highly efficient editing process.

= Migrate Your WooCommerce Products From One Store to Another =

Need to copy your WooCommerce products from one store to another?

No problem. When you export products from the source site, just download our special bundle file. This file contains import instructions in addition to your product export data.

Import this bundle file on your destination site, and WP All Import will automatically configure itself. It really couldn’t be any easier.

= Run Your Product Export on a Schedule =

A product export can play a vital role in synchronizing data with other stores or marketing platforms. These updates generally occur on a regular basis.

To support this, we allow you to schedule your product export. You can do this using cron jobs on your server, or, if you sign up for our automatic scheduling service, you can do it directly through our plugin’s interface.

Either way, you can set up your product export just once and then forget about it.

= Integrate Your Product Export with External Applications =

Zapier is a leading integration platform. It allows you to connect triggers, such as the completion of a product export, to actions. These actions can include uploading an export file to one of many file host platforms, generating an email with the export file as an attachment, etc.

Our plugin is designed to work with Zapier, meaning that you can connect your product export to any of 1000s of external applications. Talk about extensibility!

= WooCommerce Product Export Free Version =

With the free version of WooCommerce Product Export, you can:

* Export products to CSV or to a simple XML feed

* Select, rearrange, and modify basic product export fields, including fields added by 3rd-party plugins or add-ons

* Export product data, bulk edit it in a spreadsheet, and then use [WP All Import](https://wordpress.org/plugins/wp-all-import/) to import those changes back into WooCommerce

* Migrate product data from one WooCommerce store to another

= WooCommerce Product Export Premium Version =

With the premium version of WooCommerce Product Export, you get all the free features plus:

* Export product data directly to Excel (.XLS and .XLSX)

* Build custom XML feeds for your product exports, no matter how complex

* Export product data to the Google Merchant Center

* Export all WooCommerce product fields, including attributes and product variations, along with options to export only parent products, only variations, or both

* Filter your product export

* Schedule your product export

* Integrate your product export with Google Sheets and other external applications via Zapier

* Modify your product export data using custom PHP functions

* Get guaranteed technical support via email (see below)

= Premium Support =

[Upgrade to the Pro edition of WP All Export](http://www.wpallimport.com/upgrade-to-wp-all-export-pro/) for premium support.

You can submit the [support form on our website](https://www.wpallimport.com/support/) or email us at [<EMAIL>](mailto:<EMAIL>). While we try to assist users of our free version, please note that support is not guaranteed and will depend on our capacity. For premium support, purchase [WP All Export Pro](https://www.wpallimport.com/upgrade-to-wp-all-export-pro/?utm_source=dotorg&utm_medium=readme&utm_campaign=premium-support).

= Related Plugins =

[Export any WordPress data to XML/CSV](https://wordpress.org/plugins/wp-all-export/)
[Import any XML or CSV File to WordPress](https://wordpress.org/plugins/wp-all-import/)
[Import Products from any XML or CSV to WooCommerce](https://wordpress.org/plugins/woocommerce-xml-csv-product-import/)
[Custom Product Tabs for WooCommerce WP All Import Add-on](https://wordpress.org/plugins/custom-product-tabs-wp-all-import-add-on/)
[Export Orders to CSV/XML for WooCommerce](https://wordpress.org/plugins/order-export-for-woocommerce/)
[Export WordPress Users to CSV/XML](https://wordpress.org/plugins/export-wp-users-xml-csv/)

== Installation ==

Either: -

* Upload the plugin from the Plugins page in WordPress
* Unzip product-export-for-woocommerce.zip and upload the contents to /wp-content/plugins/, and then activate the plugin from the Plugins page in WordPress

== Frequently Asked Questions ==

= How do I export WooCommerce products to CSV? =

1. Go to All Export › New Export.
2. Select WooCommerce Products as the export post type.
3. Choose your export columns.
4. Leave the Export Type set to CSV File.
5. Run the export and download the export file.

= How do I export WooCommerce products to Excel? =

1. Navigate to All Export › New Export.
2. Choose WooCommerce Products as the export post type.
3. Select your export columns.
4. Change the Export Type to Excel File.
5. Complete the export and download the export file.

= How do I export WooCommerce products to XML? =

1. Create a new export via All Export › New Export.
2. Select WooCommerce Products from the dropdown list.
3. Choose the export columns.
4. Change the Export Type to Feed › Simple XML Feed.
5. Run the export and download the export file.

= Can I export WooCommerce products to Google Sheets? =

1. Go to All Export › New Export.
2. Select WooCommerce Products as the export post type.
3. Choose your export columns.
4. Run the export.
5. Connect to Google Sheets using Zapier.

= How do I export WooCommerce products to the Google Merchant Center? =

1. Go to All Export › New Export.
2. Choose WooCommerce Products as your export post type.
3. Change the Export Type to Feed › Google Merchant Center Product Feed.
4. Configure the Google Merchant Center sections.
5. Connect the export feed URL to the Google Mechant Center.

= How do I Synchronize My WooCommerce Store with the Google Merchant Center? =

As long as you set up the product export feed generated by WooCommerce Product Export so that it completes before the scheduled fetch that you’ve set up for the Merchant Center, synchronization should occur at the scheduled intervals. You might also consider setting up an email notification to verify that the export was successful.

= How do I export a single product from WooCommerce? =

1. Create a new export in All Export › New Export.
2. Select WooCommerce Products as the export post type.
3. Add a filter rule to target the desired product.
4. Configure the export columns.
5. Run the export and download the exported product.

= How do I export all WooCommerce products? =

1. Start a new export in All Export › New Export.
2. Select WooCommerce Products from the dropdown list.
3. Do NOT apply any filters.
4. Chooose your export columns.
5. Complete the export and download the export file.

= How do I export products from WooCommerce with images? =

1. Create a new export for WooCommerce products.
2. Add the image fields to the export layout.
3. Run the export.
4. Download the export file.
5. Check the contents of the export file to verify that the image data is correct.

= How do I export products and categories in WooCommerce? =

1. Set up a new export via All Export › New Export.
2. Choose WooCommerce Products as the export post type.
3. Select the export columns.
4. Include the Product categories field from Available Data > Taxonomies.
5. Complete the export and download your export file.

= How do I export product attributes in WooCommerce? =

1. Create a new export via All Export › New Export.
2. Select WooCommerce Products as the export post type.
3. Choose your export columns.
4. Include the desired attributes from Available Data > Product Data > Attributes.
5. Run the export and download the exported file.

= How do I export product variations in WooCommerce? =

1. Navigate to All Export › New Export.
2. Choose WooCommerce Products from the dropdown list.
3. Select your export columns.
4. Note that the product attribute and variant columns are selected by default.
5. Finish the export and download the export file.

= Can I export custom WooCommerce product data added by a plugin or extension? =

Yes. Our plugin automatically detects all custom fields, categories, and tags created by any theme, plugin, or WooCommerce extension. You can export this custom WooCommerce product data the same way you do core data.

= How do I schedule WooCommerce product exports? =

To schedule product exports, you can manually set up cron jobs from your server or use our automatic scheduling service directly from the interface. In addition to added convenience, the automatic service also verifies completion.

= How many WooCommerce products can I export at one time? =

You can export as many products as you wish. The only limit is the file size allowed by the server or imposed by your method of file transmission. In either case, WooCommerce Product Export allows you to avoid these constraints by splitting your product exports into multiple files based on record counts.

= Can I export products in different languages? =

Yes. By installing the [WordPress Multilingual Plugin (WPML)](https://wordpress.org/plugins/woocommerce-multilingual/), you can export WooCommerce product text in more than 40 languages.

= How do I migrate products to a different website? =

1. Export the WooCommerce products that you want to migrate.
2. Download the Bundle option, which includes import instructions.
3. Import the bundle file to the destination site.
4. WP All Import will use the bundle file to automatically configure itself.
5. Complete the import and check the migrated products.

= How do I bulk edit WooCommerce products? =

1. Export the products that you wish to edit.
2. Edit your export file in a spreadsheet software like Excel or Google Sheets.
3. Import the modified file back into WooCommerce.
4. Verify that the changes were correctly applied.

= How do I print a WooCommerce product list? =

The fastest way to print a WooCommerce product list is to go to Products > All Products and click Control+P from your browser. Just click the Print button to print your WooCommerce product list. If you want to control layout, styling, or manipulate the list data in any way, export your products to a spreadsheet and print from there.

= How often is WooCommerce Product Export updated? =

At a minimum, both the free and premium versions of the plugin are updated with each major WordPress release and often many times in between.

= How do I get support? =

The free version of WooCommerce Product Export is supported through the WordPress.org community forums. Purchasers of the premium version can email us directly. We will respond as fast as we can, usually within one business day.

== Screenshots ==

1. New WooCommerce Product Export
2. Customize WooCommerce Product Export Columns
3. WooCommerce Product Export Standard Section
4. WooCommerce Product Export Product Section
5. WooCommerce Export Products Media Section
6. WooCommerce Product Export Taxonomies Section
7. WooCommerce Product Export Custom Fields Section
8. WooCommerce Export Products Other Section
9. WooCommerce Product Export Single Filter
10. WooCommerce Product Export Multiple Filters
11. WooCommerce Product Export Advanced Options
12. WooCommerce Export Products to Excel
13. WooCommerce Export Products to XML
14. WooCommerce Export Products Confirm and Run

== Changelog ==

= 1.0.3 =
* improvement: update upgrade links

= 1.0.2 =
* improvement: don't include non-category terms in the Taxonomies section for Product exports

= 1.0.1 =
* improvement: PHP 8 compatibility

= 1.0.0 =
* Initial release on WordPress.org.
