msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: WP All Export Pro\n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. Plugin Name of the plugin/theme
#: actions/admin_menu.php:11 actions/admin_menu.php:14
#: actions/admin_menu.php:15 actions/admin_menu.php:16
#: models/export/record.php:373 views/admin/export/index.php:9
#: views/admin/export/options.php:21 views/admin/export/process.php:15
#: views/admin/export/template.php:7 views/admin/manage/index.php:4
#: views/admin/manage/update.php:21 views/admin/settings/index.php:6
msgid "WP All Export"
msgstr "WP All Export"

#: actions/admin_menu.php:11
msgid "All Export"
msgstr "すべてエクスポート"

#: actions/admin_menu.php:14
msgid "Export to XML"
msgstr "XML 形式でエクスポート"

#: actions/admin_menu.php:14
msgid "New Export"
msgstr "新規エクスポート"

#: actions/admin_menu.php:15 views/admin/export/process.php:115
#: views/admin/manage/index.php:5
msgid "Manage Exports"
msgstr "エクスポートを管理"

#: actions/admin_menu.php:16 views/admin/settings/index.php:7
msgid "Settings"
msgstr "設定"

#: views/admin/export/index.php:13 views/admin/export/options.php:25
#: views/admin/export/process.php:19 views/admin/export/template.php:12
#: views/admin/manage/update.php:25
msgid "Support"
msgstr "サポート"

#: actions/wp_ajax_dismiss_export_warnings.php:6
#: actions/wp_ajax_dismiss_export_warnings.php:10
#: actions/wp_ajax_generate_zapier_api_key.php:6
#: actions/wp_ajax_generate_zapier_api_key.php:10
#: actions/wp_ajax_get_xml_spec.php:6 actions/wp_ajax_get_xml_spec.php:10
#: actions/wp_ajax_save_functions.php:6 actions/wp_ajax_save_functions.php:10
#: actions/wp_ajax_wpae_available_rules.php:6
#: actions/wp_ajax_wpae_available_rules.php:10
#: actions/wp_ajax_wpae_filtering.php:6 actions/wp_ajax_wpae_filtering.php:10
#: actions/wp_ajax_wpae_filtering_count.php:6
#: actions/wp_ajax_wpae_filtering_count.php:10
#: actions/wp_ajax_wpae_preview.php:8 actions/wp_ajax_wpae_preview.php:12
#: actions/wp_ajax_wpallexport.php:8 actions/wp_ajax_wpallexport.php:12
#: controllers/admin/manage.php:282 controllers/admin/manage.php:317
#: controllers/admin/manage.php:354 controllers/admin/manage.php:407
#: controllers/controller.php:119 wpae_api.php:7 wpae_api.php:11
msgid "Security check"
msgstr "セキュリティーチェック"

#: actions/wp_ajax_wpae_available_rules.php:21
#: views/admin/export/blocks/filters.php:18
msgid "Select Rule"
msgstr "ルールを選択"

#: actions/wp_ajax_wpae_available_rules.php:27
#: views/admin/export/blocks/filters.php:54
#: views/admin/export/blocks/filters.php:68
msgid "In"
msgstr "イン"

#: actions/wp_ajax_wpae_available_rules.php:28
#: views/admin/export/blocks/filters.php:55
#: views/admin/export/blocks/filters.php:69
msgid "Not In"
msgstr "インではない"

#: actions/wp_ajax_wpae_available_rules.php:38
#: actions/wp_ajax_wpae_available_rules.php:63
#: actions/wp_ajax_wpae_available_rules.php:74
#: actions/wp_ajax_wpae_available_rules.php:87
#: views/admin/export/blocks/filters.php:44
#: views/admin/export/blocks/filters.php:58
msgid "equals"
msgstr "等しい"

#: actions/wp_ajax_wpae_available_rules.php:39
#: actions/wp_ajax_wpae_available_rules.php:64
#: actions/wp_ajax_wpae_available_rules.php:75
#: actions/wp_ajax_wpae_available_rules.php:88
#: views/admin/export/blocks/filters.php:45
#: views/admin/export/blocks/filters.php:59
msgid "doesn't equal"
msgstr "等しくない"

#: actions/wp_ajax_wpae_available_rules.php:40
#: views/admin/export/blocks/filters.php:60
msgid "newer than"
msgstr "新しい"

#: actions/wp_ajax_wpae_available_rules.php:41
#: views/admin/export/blocks/filters.php:61
msgid "equal to or newer than"
msgstr "等しいかそれより新しい"

#: actions/wp_ajax_wpae_available_rules.php:42
#: views/admin/export/blocks/filters.php:62
msgid "older than"
msgstr "より古く"

#: actions/wp_ajax_wpae_available_rules.php:43
#: views/admin/export/blocks/filters.php:63
msgid "equal to or older than"
msgstr "等しいかより古い"

#: actions/wp_ajax_wpae_available_rules.php:45
#: actions/wp_ajax_wpae_available_rules.php:54
#: actions/wp_ajax_wpae_available_rules.php:65
#: actions/wp_ajax_wpae_available_rules.php:94
#: views/admin/export/blocks/filters.php:50
#: views/admin/export/blocks/filters.php:64
msgid "contains"
msgstr "含む"

#: actions/wp_ajax_wpae_available_rules.php:46
#: actions/wp_ajax_wpae_available_rules.php:55
#: actions/wp_ajax_wpae_available_rules.php:66
#: actions/wp_ajax_wpae_available_rules.php:95
#: views/admin/export/blocks/filters.php:51
#: views/admin/export/blocks/filters.php:65
msgid "doesn't contain"
msgstr "含まれない"

#: actions/wp_ajax_wpae_available_rules.php:47
#: actions/wp_ajax_wpae_available_rules.php:67
#: actions/wp_ajax_wpae_available_rules.php:80
#: actions/wp_ajax_wpae_available_rules.php:96
#: views/admin/export/blocks/filters.php:52
#: views/admin/export/blocks/filters.php:66
msgid "is empty"
msgstr "空である"

#: actions/wp_ajax_wpae_available_rules.php:48
#: actions/wp_ajax_wpae_available_rules.php:68
#: actions/wp_ajax_wpae_available_rules.php:81
#: actions/wp_ajax_wpae_available_rules.php:97
#: views/admin/export/blocks/filters.php:53
#: views/admin/export/blocks/filters.php:67
msgid "is not empty"
msgstr "空ではない"

#: actions/wp_ajax_wpae_available_rules.php:76
#: actions/wp_ajax_wpae_available_rules.php:89
#: views/admin/export/blocks/filters.php:46
msgid "greater than"
msgstr "大なり"

#: actions/wp_ajax_wpae_available_rules.php:77
#: actions/wp_ajax_wpae_available_rules.php:90
#: views/admin/export/blocks/filters.php:47
msgid "equal to or greater than"
msgstr "等しいかより大きい"

#: actions/wp_ajax_wpae_available_rules.php:78
#: actions/wp_ajax_wpae_available_rules.php:91
#: views/admin/export/blocks/filters.php:48
msgid "less than"
msgstr "最小化"

#: actions/wp_ajax_wpae_available_rules.php:79
#: actions/wp_ajax_wpae_available_rules.php:92
#: views/admin/export/blocks/filters.php:49
msgid "equal to or less than"
msgstr "同等または未満"

#: actions/wp_ajax_wpae_filtering.php:35
msgid "Add Filtering Options"
msgstr "フィルタリングオプションを追加する"

#: actions/wp_ajax_wpae_filtering.php:53
msgid "Migrate %s"
msgstr "%s を移行"

#: actions/wp_ajax_wpae_filtering.php:57 actions/wp_ajax_wpae_filtering.php:64
msgid "Customize Export File"
msgstr "エクスポート ファイルをカスタマイズします。"

#: actions/wp_ajax_wpae_filtering_count.php:206
msgid "Unable to Export"
msgstr "エクスポートできません。"

#: actions/wp_ajax_wpae_filtering_count.php:207
msgid "Exporting taxonomies requires WordPress 4.6 or greater"
msgstr "ワードプレス 4.6 以上を必要とする分類のエクスポート"

#: actions/wp_ajax_wpae_filtering_count.php:264
msgid "Your export is ready to run."
msgstr "エクスポートを実行する準備が。"

#: actions/wp_ajax_wpae_filtering_count.php:265
msgid "WP All Export will export %d %s."
msgstr "WP をすべてエクスポート %d %s がエクスポートされます。"

#: actions/wp_ajax_wpae_filtering_count.php:268
#: actions/wp_ajax_wpae_filtering_count.php:271
#: actions/wp_ajax_wpae_filtering_count.php:274
#: actions/wp_ajax_wpae_filtering_count.php:290
#: actions/wp_ajax_wpae_filtering_count.php:293
#: actions/wp_ajax_wpae_filtering_count.php:296
msgid "Nothing to export."
msgstr "エクスポートなし"

#: actions/wp_ajax_wpae_filtering_count.php:269
#: actions/wp_ajax_wpae_filtering_count.php:291
msgid "All %s have already been exported."
msgstr "すべて %s は既にエクスポートされています。"

#: actions/wp_ajax_wpae_filtering_count.php:272
#: actions/wp_ajax_wpae_filtering_count.php:294
#: actions/wp_ajax_wpae_filtering_count.php:311
msgid "No matching %s found for selected filter rules."
msgstr "マッチングの %s が選択したフィルターの規則が見つかりませんでした。"

#: actions/wp_ajax_wpae_filtering_count.php:275
#: actions/wp_ajax_wpae_filtering_count.php:297
#: actions/wp_ajax_wpae_filtering_count.php:313
msgid "There aren't any %s to export."
msgstr "エクスポートするすべての %s がありません。"

#: actions/wp_ajax_wpae_filtering_count.php:287
#: views/admin/export/template.php:27
msgid "Choose data to include in the export file."
msgstr "エクスポート ファイルに含めるデータを選択します。"

#: actions/wp_ajax_wpae_filtering_count.php:309
msgid "Continue to configure and run your export."
msgstr "構成し、エクスポートを実行し続けます。"

#: actions/wp_ajax_wpae_preview.php:51 controllers/admin/export.php:271
msgid "XML template is empty."
msgstr "XML テンプレートは空です。"

#: actions/wp_ajax_wpae_preview.php:181 actions/wp_ajax_wpae_preview.php:337
msgid "Invalid XML"
msgstr "無効なXML"

#: actions/wp_ajax_wpae_preview.php:184 actions/wp_ajax_wpae_preview.php:340
msgid "Line"
msgstr "線"

#: actions/wp_ajax_wpae_preview.php:185 actions/wp_ajax_wpae_preview.php:341
msgid "Column"
msgstr "カラム"

#: actions/wp_ajax_wpae_preview.php:186 actions/wp_ajax_wpae_preview.php:342
msgid "Code"
msgstr "コード"

#: actions/wp_ajax_wpae_preview.php:245
msgid "There was a problem parsing the custom XML template"
msgstr "カスタム XML テンプレートを解析中に問題が発生しました"

#: actions/wp_ajax_wpae_preview.php:316
msgid "Can't preview the document."
msgstr "ドキュメントをプレビューできません。"

#: actions/wp_ajax_wpae_preview.php:318 actions/wp_ajax_wpae_preview.php:358
msgid "You can continue export or try to use &lt;data&gt; tag as root element."
msgstr "エクスポートを続行したり、ルート要素として < データ > タグを使用してみてください。"

#: actions/wp_ajax_wpae_preview.php:356
msgid "Can't preview the document. Root element is not detected."
msgstr "ドキュメントをプレビューできません。ルート要素が認識されません。"

#: actions/wp_ajax_wpae_preview.php:408
msgid "Data not found."
msgstr "データが見つかりません！"

#: actions/wp_ajax_wpae_preview.php:417
msgid "This format is not supported."
msgstr "このビデオ形式はサポートされていません。"

#: actions/wp_ajax_wpallexport.php:29
msgid "Export is not defined."
msgstr "エクスポートは定義されていません。"

#: actions/wp_ajax_wpallexport.php:51 actions/wp_ajax_wpallexport.php:78
#: views/admin/export/index.php:135 views/admin/export/index.php:170
msgid "Upgrade to the Pro edition of WP All Export to Export Users"
msgstr "無料版のユーザーに技術サポートを提供するために最善を尽くしていますが、Proユーザーからのリクエストに優先順位を付ける必要があります。 WP All Exportのサポートが必要な場合は、サポートフォームからチケットを提出してください。"

#: actions/wp_ajax_wpallexport.php:55 actions/wp_ajax_wpallexport.php:82
#: views/admin/export/index.php:143 views/admin/export/index.php:175
msgid "Upgrade to the Pro edition of WP All Export to Export Comments"
msgstr "プレミアムサポートのためのWP All Export のプロ版へのアップグレード"

#: controllers/admin/export.php:119
msgid "ZipArchive class is missing on your server.<br/>Please contact your web hosting provider and ask them to install and activate ZipArchive."
msgstr "ZipArchive クラスがサーバーで見つかりません。<br>Web ホスティング プロバイダーにお問い合わせください、インストールおよび ZipArchive をアクティブ化するように依頼してください。"

#: controllers/admin/export.php:123
msgid "Required PHP components are missing.<br/><br/>WP All Export requires XMLReader, and XMLWriter PHP modules to be installed.<br/>These are standard features of PHP, and are necessary for WP All Export to write the files you are trying to export.<br/>Please contact your web hosting provider and ask them to install and activate the DOMDocument, XMLReader, and XMLWriter PHP modules."
msgstr "必要な PHP コンポーネントが見つかりません。<br><br>XMLReader は、必要があります WP をすべてエクスポートと XMLWriter の PHP モジュールをインストールします。<br>これらは PHP の標準機能であり、WP すべてエクスポート エクスポートしようとしているファイルを書き込むために必要な。<br>Web ホスティング プロバイダーに連絡してインストールして DOMDocument、XMLReader と XMLWriter PHP モジュールをアクティブ化するように依頼します。"

#: controllers/admin/export.php:212 src/App/Controller/ExportController.php:102
msgid "You've reached your max_input_vars limit of %d. Please contact your web host to increase it."
msgstr "Max_input_vars 制限値の %d に達しました。それを高めるため、web ホストをお問い合わせください。"

#: controllers/admin/export.php:243
msgid "You haven't selected any columns for export."
msgstr "エクスポート用の列を選択していません。"

#: controllers/admin/export.php:247
msgid "CSV delimiter must be specified."
msgstr "CSV の区切り文字を指定する必要があります。"

#: controllers/admin/export.php:254
msgid "Main XML Tag is required."
msgstr "主な XML タグが必要です。"

#: controllers/admin/export.php:259
msgid "Single Record XML Tag is required."
msgstr "1 つのレコードの XML タグが必要です。"

#: controllers/admin/export.php:263
msgid "Main XML Tag equals to Single Record XML Tag."
msgstr "メイン XML タグは、1 つのレコードの XML タグに等しい。"

#: controllers/admin/export.php:319 controllers/admin/export.php:430
#: controllers/admin/manage.php:218
msgid "Options updated"
msgstr "テーマオプションを更新しました。"

#: controllers/admin/manage.php:56
msgid "&laquo;"
msgstr "&laquo;"

#: controllers/admin/manage.php:57
msgid "&raquo;"
msgstr "&raquo;"

#: controllers/admin/manage.php:148 views/admin/manage/index.php:298
msgid "Export canceled"
msgstr "エクスポートがキャンセルされました"

#: controllers/admin/manage.php:246
msgid "Export deleted"
msgstr "削除されたエクスポート"

#: controllers/admin/manage.php:274
msgid "%d %s deleted"
msgstr "%d 件の %s が削除されました"

#: controllers/admin/manage.php:274 views/admin/manage/bulk.php:10
msgid "export"
msgid_plural "exports"
msgstr[0] "エクスポート"

#: controllers/admin/manage.php:341
msgid "The exported bundle is missing and can't be downloaded. Please re-run your export to re-generate it."
msgstr "エクスポートされたバンドルがないため、ダウンロードできません。それを再生成するエクスポートを再実行してください。"

#: controllers/admin/manage.php:346
msgid "This export doesn't exist."
msgstr "このエクスポートが存在しません。"

#: controllers/admin/manage.php:448
msgid "File format not supported"
msgstr "サポートされていないファイル形式"

#: controllers/admin/manage.php:454 controllers/admin/manage.php:459
msgid "The exported file is missing and can't be downloaded. Please re-run your export to re-generate it."
msgstr "エクスポートされたファイルがないため、ダウンロードできません。それを再生成するエクスポートを再実行してください。"

#: controllers/admin/settings.php:28
msgid "Settings saved"
msgstr "設定保存"

#: controllers/admin/settings.php:51
msgid "Unknown File extension. Only txt files are permitted"
msgstr "不明なファイルの拡張子。唯一のTXTファイルが許可されています"

#: controllers/admin/settings.php:64
msgid "%d template imported"
msgid_plural "%d templates imported"
msgstr[0] "%d のテンプレートのインポート"

#: controllers/admin/settings.php:66
msgid "Wrong imported data format"
msgstr "間違ってインポートされたデータ形式"

#: controllers/admin/settings.php:68
msgid "File is empty or doesn't exests"
msgstr "ファイルが空または存在しません"

#: controllers/admin/settings.php:71
msgid "Undefined entry!"
msgstr "未定義のエントリ！"

#: controllers/admin/settings.php:73
msgid "Please select file."
msgstr "ファイルを選択してください。"

#: controllers/admin/settings.php:79
msgid "Templates must be selected"
msgstr "テンプレートを選択する必要があります"

#: controllers/admin/settings.php:88
msgid "%d template deleted"
msgid_plural "%d templates deleted"
msgstr[0] "%d のテンプレートは削除されました"

#: filters/wpallexport_custom_types.php:7
msgid "WooCommerce Products"
msgstr "WooCommerce 商品リスト"

#: filters/wpallexport_custom_types.php:8
msgid "WooCommerce Orders"
msgstr "WooCommerceの注文"

#: filters/wpallexport_custom_types.php:9
msgid "WooCommerce Coupons"
msgstr "WooCommerce クーポン"

#: filters/wpallexport_custom_types.php:26
msgid "WooCommerce Customers"
msgstr "WooCommerce のお客様"

#: helpers/pmxe_render_xml_element.php:44 helpers/pmxe_render_xml_text.php:10
msgid "<strong>%s</strong> %s more"
msgstr "<strong>%s</strong> %s さらに"

#: helpers/pmxe_render_xml_element.php:44 helpers/pmxe_render_xml_text.php:10
msgid "element"
msgid_plural "elements"
msgstr[0] "要素"

#: helpers/pmxe_render_xml_text.php:16
msgid "more"
msgstr "さらに"

#: helpers/wp_all_export_get_cpt_name.php:10 views/admin/export/index.php:72
msgid "Users"
msgstr "ユーザー"

#: helpers/wp_all_export_get_cpt_name.php:10
msgid "User"
msgstr "ユーザー"

#: helpers/wp_all_export_get_cpt_name.php:14
msgid "Customers"
msgstr "お客様"

#: helpers/wp_all_export_get_cpt_name.php:14
#: libraries/XmlExportWooCommerceOrder.php:1732
msgid "Customer"
msgstr "お客様"

#: helpers/wp_all_export_get_cpt_name.php:18 views/admin/export/index.php:68
msgid "Comments"
msgstr "コメント"

#: helpers/wp_all_export_get_cpt_name.php:18
msgid "Comment"
msgstr "コメント"

#: helpers/wp_all_export_get_cpt_name.php:27
msgid "Taxonomy Terms"
msgstr "分類規則"

#: helpers/wp_all_export_get_cpt_name.php:27
msgid "Taxonomy Term"
msgstr "タクソノミータームをセット"

#: helpers/wp_all_export_get_cpt_name.php:47
msgid "Records"
msgstr "レコード"

#: helpers/wp_all_export_get_cpt_name.php:47
msgid "Record"
msgstr "レコード"

#: libraries/WpaePhpInterpreterErrorHandler.php:22
msgid "An unknown error occured"
msgstr "不明なエラーが発生しました。"

#: libraries/WpaePhpInterpreterErrorHandler.php:24
#: libraries/WpaePhpInterpreterErrorHandler.php:28
msgid "PHP Error"
msgstr "PHP のエラー"

#: libraries/WpaePhpInterpreterErrorHandler.php:28
msgid "You probably forgot to close a quote"
msgstr "あなたはおそらく、引用符を閉じるのを忘れている"

#: libraries/XmlExportACF.php:1026 libraries/XmlExportACF.php:1118
#: libraries/XmlExportACF.php:1147
msgid "ACF"
msgstr "ACF"

#: libraries/XmlExportComment.php:160
msgid "Comment meta"
msgstr "コメント"

#: libraries/XmlExportEngine.php:201
msgid "Standard"
msgstr "標準"

#: libraries/XmlExportEngine.php:205
msgid "Media"
msgstr "メディア"

#: libraries/XmlExportEngine.php:209
msgid "Images"
msgstr "画像"

#: libraries/XmlExportEngine.php:259
msgid "Attachments"
msgstr "添付"

#: libraries/XmlExportEngine.php:307 libraries/XmlExportWooCommerce.php:512
#: views/admin/export/index.php:64
msgid "Taxonomies"
msgstr "分類"

#: libraries/XmlExportEngine.php:311 libraries/XmlExportWooCommerce.php:516
#: libraries/XmlExportWooCommerceOrder.php:1770
msgid "Custom Fields"
msgstr "カスタム フィールド"

#: libraries/XmlExportEngine.php:315 libraries/XmlExportUser.php:230
#: libraries/XmlExportWooCommerce.php:368
#: libraries/XmlExportWooCommerceCoupon.php:176
#: libraries/XmlExportWooCommerceOrder.php:1774
msgid "Other"
msgstr "その他"

#: libraries/XmlExportEngine.php:322
msgid "Author"
msgstr "販売者"

#: libraries/XmlExportEngine.php:435
msgid "WP Query field is required"
msgstr "WP クエリ フィールドは必須"

#: libraries/XmlExportEngine.php:668 libraries/XmlExportEngine.php:714
#: libraries/XmlExportWooCommerceOrder.php:1480
#: libraries/XmlExportWooCommerceOrder.php:1518
msgid "All"
msgstr "すべて"

#: libraries/XmlExportEngine.php:823
msgid "User Role"
msgstr "ユーザー権限"

#: libraries/XmlExportEngine.php:1016
#: libraries/XmlExportWooCommerceOrder.php:1620
msgid "SQL Query"
msgstr "SQL クエリー"

#: libraries/XmlExportEngine.php:1052
msgid "Missing custom XML template header."
msgstr "カスタム XML テンプレート ヘッダーがありません。"

#: libraries/XmlExportEngine.php:1057
msgid "Missing custom XML template post loop."
msgstr "カスタム XML テンプレート記事ループがありません。"

#: libraries/XmlExportEngine.php:1062
msgid "Missing custom XML template footer."
msgstr "カスタム XML テンプレート フッターがありません。"

#: src/Pro/Filtering/FilteringFactory.php:46
msgid "Filtering Options"
msgstr "フィルタリングオプションを追加する"

#: libraries/XmlExportTaxonomy.php:128
msgid "Term Meta"
msgstr "タームメタ"

#: libraries/XmlExportUser.php:212 libraries/XmlExportUser.php:223
msgid "Address"
msgstr "アドレス"

#: libraries/XmlExportUser.php:321 libraries/XmlExportWooCommerceOrder.php:1867
msgid "Customer User ID"
msgstr "お客様のユーザー ID"

#: libraries/XmlExportWooCommerce.php:372
#: libraries/XmlExportWooCommerce.php:502
msgid "Product Data"
msgstr "商品データ"

#: libraries/XmlExportWooCommerce.php:376
#: libraries/XmlExportWooCommerce.php:520
msgid "Attributes"
msgstr "属性"

#: libraries/XmlExportWooCommerce.php:524
msgid "Advanced"
msgstr "高度"

#: libraries/XmlExportWooCommerceOrder.php:951
#: views/admin/export/template/add_new_field.php:21
msgid "Upgrade to the Pro edition of WP All Export to Export Order Data"
msgstr "スケジュールエクスポートのための「WP All Export」のPro版へのアップグレード"

#: libraries/XmlExportWooCommerceOrder.php:1480
msgid "Data"
msgstr "日付"

#: libraries/XmlExportWooCommerceOrder.php:1728
msgid "Order"
msgstr "順序"

#: libraries/XmlExportWooCommerceOrder.php:1736
msgid "Items"
msgstr "アイテム"

#: libraries/XmlExportWooCommerceOrder.php:1741
msgid "Taxes & Shipping"
msgstr "税・送料"

#: libraries/XmlExportWooCommerceOrder.php:1745
msgid "Fees & Discounts"
msgstr "料金・割引"

#: libraries/XmlExportWooCommerceOrder.php:1749
#: views/admin/manage/scheduling.php:47
msgid "Notes"
msgstr "注釈"

#: libraries/XmlExportWooCommerceOrder.php:1751
msgid "Note Content"
msgstr "メモの内容"

#: libraries/XmlExportWooCommerceOrder.php:1752
msgid "Note Date"
msgstr "日付メモ"

#: libraries/XmlExportWooCommerceOrder.php:1753
msgid "Note Visibility"
msgstr "表示メモ"

#: libraries/XmlExportWooCommerceOrder.php:1754
msgid "Note User Name"
msgstr "ユーザー名​メモ"

#: libraries/XmlExportWooCommerceOrder.php:1755
msgid "Note User Email"
msgstr "ユーザー メールアドレスメモ"

#: libraries/XmlExportWooCommerceOrder.php:1759
msgid "Refunds"
msgstr "払戻"

#: libraries/XmlExportWooCommerceOrder.php:1761
msgid "Refund Total"
msgstr "払戻総額"

#: libraries/XmlExportWooCommerceOrder.php:1762
msgid "Refund ID"
msgstr "払戻 ID"

#: libraries/XmlExportWooCommerceOrder.php:1763
msgid "Refund Amounts"
msgstr "払戻金額"

#: libraries/XmlExportWooCommerceOrder.php:1764
msgid "Refund Reason"
msgstr "返金理由"

#: libraries/XmlExportWooCommerceOrder.php:1765
msgid "Refund Date"
msgstr "払戻日付"

#: libraries/XmlExportWooCommerceOrder.php:1766
msgid "Refund Author Email"
msgstr "作成者のメール アドレスに返金"

#: libraries/XmlExportWooCommerceOrder.php:1789
msgid "Order ID"
msgstr "ご注文ID"

#: libraries/XmlExportWooCommerceOrder.php:1790
msgid "Order Key"
msgstr "注文キー"

#: libraries/XmlExportWooCommerceOrder.php:1791
msgid "Order Date"
msgstr "注文日時"

#: libraries/XmlExportWooCommerceOrder.php:1792
msgid "Completed Date"
msgstr "完了日"

#: libraries/XmlExportWooCommerceOrder.php:1793
msgid "Title"
msgstr "タイトル"

#: libraries/XmlExportWooCommerceOrder.php:1794
msgid "Order Status"
msgstr "注文状況"

#: libraries/XmlExportWooCommerceOrder.php:1795
msgid "Order Currency"
msgstr "注文通貨"

#: libraries/XmlExportWooCommerceOrder.php:1796
msgid "Payment Method Title"
msgstr "決済方法名"

#: libraries/XmlExportWooCommerceOrder.php:1797
msgid "Order Total"
msgstr "お支払い合計"

#: libraries/XmlExportWooCommerceOrder.php:1810
#: views/admin/export/template/advanced_field_options.php:51
msgid "Product ID"
msgstr "商品ID"

#: libraries/XmlExportWooCommerceOrder.php:1811
msgid "SKU"
msgstr "SKU"

#: libraries/XmlExportWooCommerceOrder.php:1812
#: views/admin/export/template/advanced_field_options.php:52
msgid "Product Name"
msgstr "商品名"

#: libraries/XmlExportWooCommerceOrder.php:1813
msgid "Product Variation Details"
msgstr "製品バリエーションの詳細"

#: libraries/XmlExportWooCommerceOrder.php:1814
msgid "Quantity"
msgstr "数"

#: libraries/XmlExportWooCommerceOrder.php:1815
msgid "Item Cost"
msgstr "単価"

#: libraries/XmlExportWooCommerceOrder.php:1816
msgid "Item Total"
msgstr "商品合計"

#: libraries/XmlExportWooCommerceOrder.php:1817
msgid "Item Tax"
msgstr "アイテム税"

#: libraries/XmlExportWooCommerceOrder.php:1818
msgid "Item Tax Total"
msgstr "税合計"

#: libraries/XmlExportWooCommerceOrder.php:1819
msgid "Item Tax Data"
msgstr "アイテム税データ"

#: libraries/XmlExportWooCommerceOrder.php:1837
msgid "Rate Code (per tax)"
msgstr "レートコード(税) "

#: libraries/XmlExportWooCommerceOrder.php:1838
msgid "Rate Percentage (per tax)"
msgstr "レートパーセンテージ (税) "

#: libraries/XmlExportWooCommerceOrder.php:1839
msgid "Amount (per tax)"
msgstr "金額 (税) "

#: libraries/XmlExportWooCommerceOrder.php:1840
msgid "Total Tax Amount"
msgstr "合計税額"

#: libraries/XmlExportWooCommerceOrder.php:1841
msgid "Shipping Method"
msgstr "配送方法"

#: libraries/XmlExportWooCommerceOrder.php:1842
msgid "Shipping Cost"
msgstr "送料を追加"

#: libraries/XmlExportWooCommerceOrder.php:1843
msgid "Shipping Taxes"
msgstr "送料税金"

#: libraries/XmlExportWooCommerceOrder.php:1852
msgid "Discount Amount (per coupon)"
msgstr "割引額(クーポン) "

#: libraries/XmlExportWooCommerceOrder.php:1853
msgid "Coupons Used"
msgstr "使用済みクーポン"

#: libraries/XmlExportWooCommerceOrder.php:1854
msgid "Total Discount Amount"
msgstr "合計値引き額"

#: libraries/XmlExportWooCommerceOrder.php:1855
msgid "Fee Amount (per surcharge)"
msgstr "手数料の額(有料) "

#: libraries/XmlExportWooCommerceOrder.php:1856
msgid "Total Fee Amount"
msgstr "総手数料額"

#: libraries/XmlExportWooCommerceOrder.php:1857
msgid "Fee Taxes"
msgstr "税"

#: libraries/XmlExportWooCommerceOrder.php:1868
msgid "Customer Note"
msgstr "顧客メモ"

#: libraries/XmlExportWooCommerceOrder.php:1923
msgid "Billing Email Address"
msgstr "請求先のメールアドレス"

#: libraries/XmlExportWooCommerceOrder.php:1924
msgid "Customer Account Email Address"
msgstr "お客様メールアドレス"

#: models/export/record.php:443
msgid "The other two files in this zip are the export file containing all of your data and the import template for WP All Import. \n"
"\n"
"To import this data, create a new import with WP All Import and upload this zip file."
msgstr "この zip ファイルに他の 2 つのファイルは、WP をすべてインポートのすべてのあなたのデータとテンプレートのインポートを含むエクスポート ファイルです。\n"
"\n"
"このデータをインポートするには、WP をすべてインポートと新しいインポートを作成し、この zip ファイルをアップロードします。"

#: views/admin/export/blocks/filters.php:2
msgid "Upgrade to the Pro edition of WP All Export to Add Filters"
msgstr "Zapier 統合のためにWP All ExportのPro版へのアップグレード"

#: views/admin/export/blocks/filters.php:3 views/admin/export/index.php:136
#: views/admin/export/index.php:140 views/admin/export/index.php:144
#: views/admin/export/index.php:148 views/admin/export/index.php:171
#: views/admin/export/index.php:176 views/admin/export/template.php:345
#: views/admin/export/template.php:471 views/admin/export/template.php:526
#: views/admin/export/template/add_new_field.php:22
#: views/admin/export/template/advanced_field_options.php:63
#: views/admin/manage/scheduling.php:7 views/admin/settings/index.php:111
#: views/admin/settings/index.php:135
msgid "If you already own it, remove the free edition and install the Pro edition."
msgstr "WP All Export のプロ版にアップグレードし、ファンクションエディタを有効にする"

#: views/admin/export/blocks/filters.php:4
msgid "Element"
msgstr "要素"

#: views/admin/export/blocks/filters.php:5
msgid "Rule"
msgstr "ルール"

#: views/admin/export/blocks/filters.php:6
msgid "Value"
msgstr "値"

#: views/admin/export/blocks/filters.php:12
msgid "Select Element"
msgstr "要素を選択"

#: views/admin/export/blocks/filters.php:25
msgid "Add Rule"
msgstr "ルールを追加"

#: views/admin/export/blocks/filters.php:37
msgid "Date filters use natural language.<br>For example, to return records created in the last week: <i>date ▸ newer than ▸ last week</i>.<br>For all records created in 2016: <i>date ▸ older than ▸ 1/1/2017</i> AND <i>date ▸ newer than ▸ 12/31/2015</i>"
msgstr "日付フィルターは、自然言語を使用します。<br>たとえば、最後の週に作成されたレコードを返す:<i>日付 ▸ ▸ 先週よりも新しい</i>。<br>2016 年に作成されるすべてのレコード: <i>▸ ▸ より古い日 2017/01/01</i>と<i>▸ ▸ より新しい日付 2015/12/31</i>"

#: views/admin/export/blocks/filters.php:38
msgid "No filtering options. Add filtering options to only export records matching some specified criteria."
msgstr "フィルタ リング オプションはありません。抽出条件を指定したフィルタ リングのいくつかに一致するレコードをエクスポートするオプションを追加します。"

#: views/admin/export/blocks/filters.php:122
msgid "Variable product matching rules: "
msgstr "変数マッチングルール:␣"

#: views/admin/export/blocks/filters.php:124
msgid "Strict"
msgstr "厳格"

#: views/admin/export/blocks/filters.php:125
msgid "Permissive"
msgstr "緩やか"

#: views/admin/export/blocks/filters.php:127
msgid "Strict matching requires all variations to pass in order for the product to be exported. Permissive matching allows the product to be exported if any of the variations pass."
msgstr "厳密なマッチングでは、製品をエクスポートするためにすべてのバリエーションを渡す必要があります。 許容一致は、バリエーションのいずれかが合格した場合に、製品をエクスポートすることを可能にする。"

#: views/admin/export/index.php:10 views/admin/export/options.php:22
#: views/admin/export/process.php:16 views/admin/export/template.php:8
#: views/admin/manage/update.php:22
msgid "Export to XML / CSV"
msgstr "XML / CSVにエクスポート"

#: views/admin/export/index.php:13 views/admin/export/options.php:25
#: views/admin/export/process.php:19 views/admin/export/template.php:15
#: views/admin/manage/update.php:25
msgid "Documentation"
msgstr "ドキュメンテーション"

#: views/admin/export/index.php:30
msgid "First, choose what to export."
msgstr "まず、エクスポートするものを選択します。"

#: views/admin/export/index.php:33
msgid "Specific Post Type"
msgstr "特定の投稿タイプ"

#: views/admin/export/index.php:37
msgid "WP_Query Results"
msgstr "WP_Query 結果"

#: views/admin/export/index.php:92
msgid "Choose a post type..."
msgstr "投稿タイプを選択"

#: views/admin/export/index.php:127
msgid "Select taxonomy"
msgstr "タクソノミーを選択"

#: views/admin/export/index.php:139
msgid "Upgrade to the Pro edition of WP All Export to Export Customers"
msgstr "「WP All Export」のプロ版へのアップグレード"

#: views/admin/export/index.php:147
msgid "Upgrade to the Pro edition of WP All Export to Export Taxonomies"
msgstr "「WP All Export」のPro版にアップグレードしてタクソノミーをエクスポートする"

#: views/admin/export/index.php:141
msgid "Post Type Query"
msgstr "投稿タイプクエリ"

#: views/admin/export/index.php:142
msgid "User Query"
msgstr "ユーザークエリ"

#: views/admin/export/index.php:147
msgid "Comment Query"
msgstr "コメントクエリ"

#: views/admin/export/index.php:189 views/admin/export/options.php:108
#: views/admin/export/process.php:120 views/admin/export/template.php:552
#: views/admin/manage/index.php:366 views/admin/manage/scheduling.php:57
#: views/admin/manage/templates.php:19 views/admin/manage/update.php:104
#: views/admin/settings/index.php:167
msgid "Created by"
msgstr "作成者"

#: views/admin/export/options.php:4 views/admin/export/options.php:55
#: views/admin/export/options.php:97 views/admin/manage/update.php:3
#: views/admin/manage/update.php:55 views/admin/manage/update.php:97
msgid "Confirm & Run Export"
msgstr "エクスポートの確認 & 実行"

#: views/admin/export/options.php:5 views/admin/export/options.php:101
#: views/admin/manage/update.php:4 views/admin/manage/update.php:95
msgid "Save Export Configuration"
msgstr "エクスポート設定を保存"

#: views/admin/export/options.php:95 views/admin/export/template.php:543
msgid "Back"
msgstr "戻る"

#: views/admin/export/options.php:100 views/admin/export/template.php:540
msgid "Back to Manage Exports"
msgstr "エクスポートの管理に戻る"

#: views/admin/export/options/settings.php:4
msgid "Configure Advanced Settings"
msgstr "高度な設定を構成"

#: views/admin/export/options/settings.php:12
msgid "In each iteration, process"
msgstr "各反復において、プロセス"

#: views/admin/export/options/settings.php:12
#: views/admin/export/options/settings.php:18
msgid "records"
msgstr "レコード"

#: views/admin/export/options/settings.php:13
msgid "WP All Export must be able to process this many records in less than your server's timeout settings. If your export fails before completion, to troubleshoot you should lower this number."
msgstr "WP All Exportは、多くのレコードをサーバーのタイムアウト設定未満で処理できる必要があります。 完了前にエクスポートが失敗した場合は、トラブルシューティングのためにこの数を減らす必要があります"

#: views/admin/export/options/settings.php:18
msgid "Only export %s once"
msgstr "一度 %s だけエクスポート"

#: views/admin/export/options/settings.php:19
msgid "If re-run, this export will only include records that have not been previously exported.<br><br><strong>Upgrade to the Pro edition of WP All Export to use this option.</strong>"
msgstr "再実行すると、このエクスポートには以前にエクスポートされていないレコードのみが含まれます。<br><br><strong>WP All ExportのPro版にアップグレードしてこのオプションを使用します。</strong>"

#: views/admin/export/options/settings.php:24
msgid "Include BOM in export file"
msgstr "エクスポートファイルにBOMを含める"

#: views/admin/export/options/settings.php:25
msgid "The BOM will help some programs like Microsoft Excel read your export file if it includes non-English characters."
msgstr "BOM は、英語以外の文字が含まれている場合、Microsoft Excel は、エクスポート ファイルを読むようないくつかのプログラムに役立ちます。"

#: views/admin/export/options/settings.php:30
msgid "Create a new file each time export is run"
msgstr "各時間のエクスポートを実行する新しいファイルを作成します。"

#: views/admin/export/options/settings.php:31
msgid "If disabled, the export file will be overwritten every time this export run."
msgstr "無効の場合、エクスポート ファイルはこのエクスポートを実行するたびに上書きされます。"

#: views/admin/export/options/settings.php:36
msgid "Split large exports into multiple files"
msgstr "大量のエクスポートを複数のファイルに分割"

#: views/admin/export/options/settings.php:39
msgid "Limit export to"
msgstr "エクスポートを制限"

#: views/admin/export/options/settings.php:39
msgid "records per file"
msgstr "ファイルあたりのレコード"

#: views/admin/export/options/settings.php:47
msgid "Friendly Name:"
msgstr "フレンドリ名："

#: views/admin/export/options/settings.php:48
msgid "Save friendly name..."
msgstr "フレンドリ名を保存..."

#: views/admin/export/process.php:28
msgid "Export <span id=\"status\">in Progress...</span>"
msgstr "エクスポートの<span id=\"status\">処理中...</span>"

#: views/admin/export/process.php:29
msgid "Exporting may take some time. Please do not close your browser or refresh the page until the process is complete."
msgstr "エクスポートには時間がかかることがあります。 処理が完了するまでブラウザを閉じたり、ページを更新したりしないでください。"

#: views/admin/export/process.php:36
msgid "Time Elapsed"
msgstr "経過時間"

#: views/admin/export/process.php:38 views/admin/export/process.php:73
msgid "Exported"
msgstr "エクスポートしました"

#: views/admin/export/process.php:72
msgid "Export %ss"
msgstr "%ss をエクスポート"

#: views/admin/export/process.php:84
msgid "WP All Export successfully exported your data!"
msgstr "WP All Exportはあなたのデータを正常にエクスポート！"

#: views/admin/export/process.php:94
msgid "Download Data"
msgstr "ダウンロードデータ"

#: views/admin/export/process.php:101 views/admin/manage/index.php:152
msgid "Split %ss"
msgstr "%s で区切る"

#: views/admin/export/process.php:106 views/admin/manage/index.php:140
#: views/admin/manage/index.php:147
msgid "Bundle"
msgstr "バンドル"

#: views/admin/export/process.php:107
msgid "Settings & Data for WP All Import"
msgstr "WP All Importの設定 & データ"

#: views/admin/export/template.php:68
msgid "Upgrade to the Pro edition of WP All Export to Select Product Variation Options"
msgstr "WP All ExportのPro版にアップグレードして製品バリエーションの選択"

#: views/admin/export/template.php:147
msgid "Drag & drop data from \"Available Data\" on the right to include it in the export or click \"Add Field To Export\" below."
msgstr "右側の「利用可能なデータ」からデータをドラッグ＆ドロップしてエクスポートに含めるか、「エクスポートするフィールドを追加」をクリックします。"

#: views/admin/export/template.php:172
msgid "Warning: without %s you won't be able to re-import this data back to this site using WP All Import."
msgstr "警告：%s がなければ、WP All Importを使用してこのデータをこのサイトに再インポートすることはできません。"

#: views/admin/export/template.php:189
msgid "Add Field"
msgstr "フィールドを追加"

#: views/admin/export/template.php:191
msgid "Add All"
msgstr "すべて追加"

#: views/admin/export/template.php:193
msgid "Clear All"
msgstr "すべてをクリア"

#: views/admin/export/template.php:199 views/admin/export/template.php:404
msgid "Preview"
msgstr "プレビュー"

#: views/admin/export/template.php:209 views/admin/export/template.php:267
#: views/admin/export/template.php:413
msgid "Advanced Options"
msgstr "高度な設定"

#: views/admin/export/template.php:216
msgid "Root XML Element"
msgstr "XML のルート要素"

#: views/admin/export/template.php:225
msgid "Single %s XML Element"
msgstr "シングル %s の XML 要素"

#: views/admin/export/template.php:236 views/admin/export/template.php:421
msgid "There are certain characters that cannot be included in an XML file unless they are wrapped in CDATA tags.<br/><a target='_blank' href='%s'>Click here to read more about CDATA tags.</a>"
msgstr "XMLファイルに含まれていない特定の文字がCDATAタグで囲まれていない場合、その文字はCDATAタグで囲まれていません。<br/><a target='_blank' href='%s'> CDATAタグの詳細については、ここをクリックしてください。</a>"

#: views/admin/export/template.php:239 views/admin/export/template.php:428
msgid "Automatically wrap data in CDATA tags when it contains illegal characters"
msgstr "無効な文字が含まれている場合、データを CDATA タグで自動的に折り返す"

#: views/admin/export/template.php:243 views/admin/export/template.php:436
msgid "Always wrap data in CDATA tags"
msgstr "常にデータを CDATA タグでラップします。"

#: views/admin/export/template.php:247 views/admin/export/template.php:444
msgid "Never wrap data in CDATA tags"
msgstr "決してデータを CDATA タグでラップします。"

#: views/admin/export/template.php:249 views/admin/export/template.php:447
msgid "Warning: This may result in an invalid XML file"
msgstr "警告: これは無効な XML ファイルで可能性があります。"

#: views/admin/export/template.php:274
msgid "Separator:"
msgstr "区切り:"

#: views/admin/export/template.php:296
msgid "Display each product in its own row"
msgstr "独自の行にそれぞれの製品を表示します。"

#: views/admin/export/template.php:297
msgid "If an order contains multiple products, each product will have its own row. If disabled, each product will have its own column."
msgstr "注文には、複数の製品が含まれています、各製品は、独自の行があります。無効にした各製品は、独自の列があります。"

#: views/admin/export/template.php:301
#: views/admin/export/template/advanced_field_options.php:15
msgid "Fill in empty columns"
msgstr "空の列に入力します。"

#: views/admin/export/template.php:303
msgid "If enabled, each order item will appear as its own row with all order info filled in for every column. If disabled, order info will only display on one row with only the order item info displaying in additional rows."
msgstr "有効な場合、注文の各項目は、すべての列満たされたすべてのオーダー情報を独自の行として表示されます。無効、注文情報だけ追加の行に表示するオーダー商品情報のみを 1 つの行に表示されます。"

#: views/admin/export/template.php:309
msgid "Export File Type"
msgstr "エクスポートファイルの種類"

#: views/admin/export/template.php:314
msgid "Choose your export file type"
msgstr "エクスポートファイルの種類を選択する"

#: views/admin/export/template.php:326
msgid "Spreadsheet"
msgstr "スプレッドシート"

#: views/admin/export/template.php:320
msgid "XML Feed"
msgstr "XML フィード"

#: views/admin/export/template.php:345
msgid "CSV File"
msgstr "CSVファイル"

#: views/admin/export/template.php:346
msgid "Excel File (XLS)"
msgstr "Excel ファイル (XLS)"

#: views/admin/export/template.php:347
msgid "Excel File (XLSX)"
msgstr "Excel ファイル (XLSX)"

#: views/admin/export/template.php:344
msgid "Upgrade to the Pro edition of WP All Export to Export to Excel"
msgstr "「WP All Export」のPro版にアップグレードしてExcelにエクスポートする"

#: views/admin/export/template.php:356
msgid "Simple XML Feed"
msgstr "単純な XML フィード"

#: views/admin/export/template.php:357
msgid "Custom XML Feed"
msgstr "カスタム XML フィード"

#: views/admin/export/template.php:387
msgid "XML Editor"
msgstr "XML エディター"

#: views/admin/export/template.php:399
msgid "Help"
msgstr "ヘルプ"

#: views/admin/export/template.php:475
#: views/admin/export/template/advanced_field_options.php:73
#: views/admin/settings/index.php:150
msgid "Function Editor"
msgstr "関数エディター"

#: views/admin/export/template.php:470
#: views/admin/export/template/advanced_field_options.php:62
msgid "Upgrade to the Pro edition of WP All Export to use Custom PHP Functions"
msgstr "「WP All Export」のPro版にアップグレードし、カスタムPHP関数を使用する"

#: views/admin/export/template.php:488
#: views/admin/export/template/advanced_field_options.php:82
#: views/admin/settings/index.php:157
msgid "Save Functions"
msgstr "関数を保存"

#: views/admin/export/template.php:490
#: views/admin/export/template/advanced_field_options.php:73
#: views/admin/settings/index.php:158
msgid "Add functions here for use during your export. You can access this file at %s"
msgstr "あなたのインポート時に使用するためにここに機能を追加します。あなたは%sでこのファイルにアクセスすることができます"

#: views/admin/export/template.php:508
msgid "Save settings as a template"
msgstr "設定をテンプレートとして保存します。"

#: views/admin/export/template.php:511
msgid "Template name..."
msgstr "テンプレート名..."

#: views/admin/export/template.php:516
msgid "Load Template..."
msgstr "テンプレートを読み込みます..."

#: views/admin/export/template.php:525
msgid "Upgrade to the Pro edition of WP All Export to Export Custom XML"
msgstr "「WP All Export」のPro版にアップグレードしてカスタムXMLをエクスポートする"

#: views/admin/export/template.php:546
msgid "Continue"
msgstr "続行"

#: views/admin/export/template.php:562
msgid "Available Data"
msgstr "利用可能なデータ"

#: views/admin/export/template.php:583
msgid "Add Field To Export"
msgstr "エクスポートするフィールドを追加"

#: views/admin/export/template.php:584 views/admin/export/template.php:594
msgid "Edit Export Field"
msgstr "エクスポートフィールドの編集"

#: views/admin/export/template.php:593
msgid "Custom XML Feeds"
msgstr "カスタム XML フィード"

#: views/admin/export/template/add_new_field.php:4
msgid "What field would you like to export?"
msgstr "どのようなフィールドをエクスポートしますか?"

#: views/admin/export/template/add_new_field.php:10
msgid "What would you like to name the column/element in your exported file?"
msgstr "あなたのエクスポートされたファイル内の列/要素に名前を付けるか?"

#: views/admin/export/template/add_new_field.php:21
#: views/admin/manage/index.php:52 views/admin/manage/index.php:155
#: views/admin/manage/index.php:355
msgid "Delete"
msgstr "削除"

#: views/admin/export/template/add_new_field.php:22
msgid "Done"
msgstr "完了"

#: views/admin/export/template/add_new_field.php:23
msgid "Close"
msgstr "閉じる"

#: views/admin/export/template/advanced_field_options.php:4
msgid "%%ID%% will be replaced with the ID of the post being exported, example: SELECT meta_value FROM wp_postmeta WHERE post_id=%%ID%% AND meta_key='your_meta_key';"
msgstr "%%ID%% でエクスポートされる投稿のIDに置き換えられます, 例: SELECT meta_value FROM wp_postmeta WHERE post_id=%%ID%% AND meta_key='your_meta_key';"

#: views/admin/export/template/advanced_field_options.php:11
msgid "Display each repeater row in its own csv line"
msgstr "それぞれのリピータ行を独自のcsv行に表示する"

#: views/admin/export/template/advanced_field_options.php:16
msgid "If enabled, each repeater row will appear as its own csv line with all post info filled in for every column."
msgstr "有効にした場合、各繰り返し行はすべての列満たされたすべてのポスト情報独自の csv の行で表示されます。"

#: views/admin/export/template/advanced_field_options.php:24
msgid "Export featured image"
msgstr "注目のイメージをエクスポートします。"

#: views/admin/export/template/advanced_field_options.php:29
msgid "Export attached images"
msgstr "添付画像をエクスポートします。"

#: views/admin/export/template/advanced_field_options.php:31
msgid "Separator"
msgstr "区切り"

#: views/admin/export/template/advanced_field_options.php:39
msgid "UNIX timestamp - PHP time()"
msgstr "UNIX タイムスタンプ - PHP time()"

#: views/admin/export/template/advanced_field_options.php:40
msgid "Natural Language PHP date()"
msgstr "自然言語 PHP date()"

#: views/admin/export/template/advanced_field_options.php:43
msgid "date() Format"
msgstr "date() Format"

#: views/admin/export/template/advanced_field_options.php:50
#: views/admin/export/template/custom_xml_help.php:58
msgid "Product SKU"
msgstr "商品 SKU"

#: views/admin/export/template/advanced_field_options.php:58
msgid "Export the value returned by a PHP function"
msgstr "PHP関数から返された値をエクスポートする"

#: views/admin/export/template/advanced_field_options.php:59
msgid "The value of the field chosen for export will be passed to the PHP function."
msgstr "エクスポート用に選択されたフィールドの値は、PHP関数に渡されます。"

#: views/admin/export/template/custom_xml_help.php:3
msgid "The custom XML editor makes it easy to create an XML file with the exact structure you need. The syntax is simple and straightforward, yet powerful enough to allow you to pass your data through custom PHP functions."
msgstr "カスタム XML エディター、必要な正確な構造を持つ XML ファイルを作成しやすくなります。構文は、単純な簡単なまだカスタムの PHP 関数を使ってデータを渡すことができます強力です。"

#: views/admin/export/template/custom_xml_help.php:5
msgid "Custom XML Editor"
msgstr "カスタム XML エディター"

#: views/admin/export/template/custom_xml_help.php:8
msgid "The custom XML editor is a template for your custom XML feed. Everything between the <span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_comment\">&lt;!-- BEGIN LOOP --&gt;</span> and <span class=\"wp_all_export_code_comment\">&lt;!-- END LOOP --&gt;</span></span> tags will be repeated for each exported post."
msgstr "カスタムの XML エディターは、カスタム XML フィードのテンプレートです。<span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_comment\">&lt;!-- BEGIN LOOP --&gt;</span> と<span class=\"wp_all_export_code_comment\">&lt;!-- END LOOP --&gt;</span></span>の間のすべてのタグを記事毎にエクスポートが繰り返されます。"

#: views/admin/export/template/custom_xml_help.php:9
msgid "You can drag and drop elements from Available Data on the right into the editor on the left. You can also manually enter data into the export template."
msgstr "右側の利用可能なデータから要素を左のエディタにドラッグ＆ドロップできます。 エクスポートテンプレートに手動でデータを入力することもできます。"

#: views/admin/export/template/custom_xml_help.php:10
msgid "For example, to add the post title to your export, you can either drag the title element into the editor, or you can manually edit the export template in editor to add it like this: <span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_tag\">&lt;my_custom_title&gt;<span class=\"wp_all_export_code_text\">{Title}</span>&lt;/my_custom_title&gt;</span></span>"
msgstr "たとえば、エクスポートする記事のタイトルを追加するか、エディターにタイトル要素をドラッグすることができますまたはこのような追加するエディターでエクスポート テンプレートを手動で編集することができます: <span class=\"wp_all_export_code\"> <span class=\"wp_all_export_code_tag\">< my_custom_title ><span class=\"wp_all_export_code_text\">{タイトル}</span></my_custom_title ></span></span>"

#: views/admin/export/template/custom_xml_help.php:13
msgid "PHP Functions"
msgstr "PHP の関数"

#: views/admin/export/template/custom_xml_help.php:16
msgid "To add a custom PHP function to your XML template wrap it in brackets: <span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_text\">[my_function({Content})]"
msgstr "カスタムPHP関数をXMLテンプレートに追加するには、大括弧で囲みます。: <span class=\"wp_all_export_code\"> <span class=\"wp_all_export_code_text\">[my_function({Content})]</span></span>"

#: views/admin/export/template/custom_xml_help.php:17
msgid "You can also use native PHP functions: <span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_text\">[str_replace(\",\",\"\",{Price})]"
msgstr "ネイティブPHP関数を使用することもできます：<span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_text\">[str_replace(\",\",\"\",{Price})]"

#: views/admin/export/template/custom_xml_help.php:18
msgid "Whatever your function returns will appear in your exported XML file. You can pass as many elements as you like to your function so that they can be combined and processed in any way."
msgstr "どのような関数は、エクスポートされた XML ファイルに表示されます返します。よう関数に結合し、任意の方法で処理できるように多くの要素を渡すことができます。"

#: views/admin/export/template/custom_xml_help.php:21
msgid "Repeating Fields and Arrays"
msgstr "繰り返しフィールドと配列"

#: views/admin/export/template/custom_xml_help.php:24
msgid "Some fields, like images, have multiple values per post. WP All Export turns these fields into indexed arrays. Whenever WP All Export encounters an indexed array in an XML element it will repeat that element once for every value in the array."
msgstr "イメージのようないくつかのフィールドは、ポストごとの複数の値を持ちます。インデックス付き配列にこれらのフィールドに WP をすべてエクスポートします。WP をすべてエクスポート XML 要素の配列を検出すると、配列のすべての値の要素が一度繰り返されます。"

#: views/admin/export/template/custom_xml_help.php:25
msgid "For example, let's assume a post as two images attached to it - image1.jpg and image2.jpg - and we want to have one XML element for every image URL. Here's what our XML template will look like:"
msgstr "たとえば、2つの画像が添付されていると仮定します。すべての画像URLに1つのXML要素が必要です。 XMLテンプレートは次のようになります。 - image1.jpg and image2.jpg -"

#: views/admin/export/template/custom_xml_help.php:33
msgid "And here's how our exported XML file will look:"
msgstr "エクスポートされたXMLファイルは次のようになります："

#: views/admin/export/template/custom_xml_help.php:41
msgid "WP All Export will do this with all indexed arrays that it comes across. So if you have a function that returns an indexed array, that XML element will be repeated for each value. Likewise, you can take a field like {Image URL} and turn it into a string, like this:"
msgstr "WP All Exportは、索引付けされたすべての配列でこれを行います。 したがって、インデックス付きの配列を返す関数がある場合、そのXML要素は各値に対して繰り返されます。 同様に、{Image URL} のようなフィールドをとり、これを次のように文字列に変換することができます："

#: views/admin/export/template/custom_xml_help.php:45
msgid "And you'll just get one XML element with all of the values, like this:"
msgstr "そして、次のようにすべての値を持つXML要素を1つだけ取得します："

#: views/admin/export/template/custom_xml_help.php:51
msgid "Example Template"
msgstr "テンプレートの例"

#: views/admin/export/template/custom_xml_help.php:55
msgid "Let's say we want to make an XML feed of our WooCommerce products with these requirements:"
msgstr "WooCommerce製品のXMLフィードに、次の要件を設定したいとしましょう。"

#: views/admin/export/template/custom_xml_help.php:57
msgid "Site name below the header, before the <span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_tag\">&lt;products&gt;</span></span> element"
msgstr "ヘッダーの下のサイト名で<span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_tag\">&lt;products&gt;</span></span> の前にある要素"

#: views/admin/export/template/custom_xml_help.php:59
msgid "Product Title"
msgstr "プロダクトタイトル"

#: views/admin/export/template/custom_xml_help.php:60
msgid "Product Price (processed via a PHP function so that they end in .99)"
msgstr "商品価格 (.99 で終了するように PHP の関数を介して処理)"

#: views/admin/export/template/custom_xml_help.php:61
msgid "Product image URLs wrapped in an <span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_tag\">&lt;images&gt;</span></span> element"
msgstr "商品画像の<span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_tag\">&lt;images&gt;</span></span>要素にラップされたURL"

#: views/admin/export/template/custom_xml_help.php:63
msgid "Here's what our XML template will look like in the editor:"
msgstr "ここでは、エディターで、XML テンプレートのようです。"

#: views/admin/export/template/custom_xml_help.php:81
msgid "Then in the Function Editor we'd define my_price_function() like so:"
msgstr "my_price_function() 定義関数エディターで、次のよう。"

#: views/admin/export/template/custom_xml_help.php:89
msgid "If we had two products, each with two images, here's what our XML file would look like:"
msgstr "2 つの製品は、それぞれ 2 つの画像があればここで私たちの XML ファイルがどのように見えるかです。"

#: views/admin/export/variation_options.php:7
msgid "Product Variations"
msgstr "製品バリエーション CSV の解析を終えた。"

#: views/admin/export/variation_options.php:10
msgid "WooCommerce stores each product variation as a separate product in the database, along with a parent product to tie all of the variations together.<br/><br/>If the product title is 'T-Shirt', then the parent product will be titled 'T-Shirt', and in the database each size/color combination will be a separate product with a title like 'Variation #23 of T-Shirt'."
msgstr "WooCommerceは、親製品のバリエーションのすべてを結びつけると共に、データベースに別個の製品として各製品バリエーションを格納します。<br><br>製品タイトルが 'T-Shirt' の場合、親製品 't シャツ' をタイトルしてデータベースの各サイズ/色の組み合わせ  'Variation #23 of T-Shirt'.のようなタイトルで別の製品になります。"

#: views/admin/export/variation_options.php:16
msgid "Only export product variations"
msgstr "商品のバリエーションのみをエクスポート"

#: views/admin/export/variation_options.php:27
#: views/admin/export/variation_options.php:61
msgid "Product variations use the parent product title"
msgstr "商品のバリエーションは親製品のタイトルを使用します"

#: views/admin/export/variation_options.php:38
#: views/admin/export/variation_options.php:72
msgid "Product variations use the default variation product title"
msgstr "商品のバリエーションは、デフォルトのバリエーション商品のタイトルを使用します"

#: views/admin/export/variation_options.php:50
msgid "Export product variations and their parent products"
msgstr "商品バリエーションとその親商品のエクスポート"

#: views/admin/export/variation_options.php:82
msgid "Only export parent products"
msgstr "親商品のみをエクスポートする"

#: views/admin/help/index.php:1
msgid "WP All Export Support"
msgstr "WP All Export のサポート"

#: views/admin/help/index.php:13
msgid "Thank you for using WP All Export."
msgstr "WP All Exportをご利用いただき、ありがとうございます。"

#: views/admin/help/index.php:15
msgid "While we do our best to provide technical support to users of the free version, we must prioritize requests from Pro users. If you need help with WP All Export please submit a ticket through the support form."
msgstr "無料版のユーザーに技術サポートを提供するために最善を尽くしていますが、Proユーザーからのリクエストに優先順位を付ける必要があります。 WP All Exportのサポートが必要な場合は、サポートフォームからチケットを提出してください。"

#: views/admin/help/index.php:17
msgid "Upgrade to the Pro edition of WP All Export for Premium Support"
msgstr "プレミアムサポートのためにWP All Exportのプロ版へのアップグレード"

#: views/admin/manage/bulk.php:10
msgid "Are you sure you want to delete <strong>%s</strong> selected %s?"
msgstr "あなたは<strong>%s</strong>選択の%sを削除してもよろしいですか？"

#: views/admin/manage/delete.php:1
msgid "Delete Export"
msgstr "エクスポートを削除します。"

#: views/admin/manage/delete.php:4
msgid "Are you sure you want to delete <strong>%s</strong> export?"
msgstr "<strong>%s</strong>のエクスポートを削除するよろしいですか。"

#: views/admin/manage/index.php:18 views/admin/manage/index.php:20
msgid "Search Exports"
msgstr "検索輸出"

#: views/admin/manage/index.php:27
msgid "ID"
msgstr "ID"

#: views/admin/manage/index.php:28
msgid "Name"
msgstr "名称"

#: views/admin/manage/index.php:30
msgid "Query"
msgstr "クエリ"

#: views/admin/manage/index.php:32
msgid "Summary"
msgstr "要約"

#: views/admin/manage/index.php:34
msgid "Info & Options"
msgstr "情報＆オプション"

#: views/admin/manage/index.php:51 views/admin/manage/index.php:353
msgid "Bulk Actions"
msgstr "一括操作"

#: views/admin/manage/index.php:54 views/admin/manage/index.php:361
msgid "Apply"
msgstr "適用"

#: views/admin/manage/index.php:60
msgid "Displaying %s&#8211;%s of %s"
msgstr "%s&#8211;%s の %sを表示しています"

#: views/admin/manage/index.php:104
msgid "No previous exports found."
msgstr "以前のエクスポートが見つかりました。"

#: views/admin/manage/index.php:134
msgid "Edit Export"
msgstr "エクスポート"

#: views/admin/manage/index.php:135
msgid "Export Settings"
msgstr "設定のエクスポート"

#: views/admin/manage/index.php:163 views/admin/manage/scheduling.php:2
msgid "Cron Scheduling"
msgstr "Cron のスケジューリング"

#: views/admin/manage/index.php:209
msgid "Import with WP All Import"
msgstr "WP All Importでインポート"

#: views/admin/manage/index.php:215 views/admin/manage/templates.php:2
msgid "Download Import Templates"
msgstr "インポート テンプレートをダウンロードします。"

#: views/admin/manage/index.php:227
msgid "Post Types: "
msgstr "投稿タイプ -"

#: views/admin/manage/index.php:244
msgid "Y/m/d g:i a"
msgstr "Y年m月d日 AＭg時i分s秒"

#: views/admin/manage/index.php:254
msgid "triggered with cron"
msgstr "cronでトリガ"

#: views/admin/manage/index.php:261 views/admin/manage/index.php:276
#: views/admin/manage/index.php:290
msgid "last activity %s ago"
msgstr "%s以前の最後のアクティビティ"

#: views/admin/manage/index.php:268
msgid "currently processing with cron"
msgstr "現在のcronで処理します"

#: views/admin/manage/index.php:283
msgid "Export currently in progress"
msgstr "現在進行中のエクスポート"

#: views/admin/manage/index.php:297
msgid "Export Attempt at %s"
msgstr "%s のエクスポート試行"

#: views/admin/manage/index.php:301
msgid "Last run: %s"
msgstr "前回の実行：%s"

#: views/admin/manage/index.php:301
msgid "never"
msgstr "永遠"

#: views/admin/manage/index.php:302
msgid "%d Records Exported"
msgstr "%d 件のレコードのエクスポート"

#: views/admin/manage/index.php:304
msgid "Format: %s"
msgstr "フォーマット: %s"

#: views/admin/manage/index.php:310
msgid "settings edited since last run"
msgstr "前回の実行以降に編集された設定"

#: views/admin/manage/index.php:322
msgid "Edit"
msgstr "編集 "

#: views/admin/manage/index.php:323
msgid "Run Export"
msgstr "エクスポート実行"

#: views/admin/manage/index.php:325
msgid "Cancel Cron"
msgstr "複製をキャンセル"

#: views/admin/manage/index.php:327
msgid "Cancel"
msgstr "キャンセル"

#: views/admin/manage/index.php:357
msgid "Restore"
msgstr "復元"

#: views/admin/manage/index.php:358
msgid "Delete Permanently"
msgstr "完全削除"

#: views/admin/manage/scheduling.php:6
msgid "Upgrade to the Pro edition of WP All Export for Scheduled Exports"
msgstr "スケジュールエクスポートのためにWP All Exportのプロ版へのアップグレード"

#: views/admin/manage/scheduling.php:6
msgid "To schedule an export, you must create two cron jobs in your web hosting control panel. One cron job will be used to run the Trigger script, the other to run the Execution script."
msgstr "エクスポートをスケジュールするには、Webホスティングコントロールパネルで2つのcronジョブを作成する必要があります。 1つのcronジョブはTriggerスクリプトを実行するために使用され、もう1つはExecutionスクリプトを実行するために使用されます。"

#: views/admin/manage/scheduling.php:10
msgid "Trigger Script URL"
msgstr "トリガー スクリプト URL"

#: views/admin/manage/scheduling.php:11
msgid "Run the trigger script when you want to update your export. Once per 24 hours is recommended."
msgstr "エクスポートを更新する場合は、トリガーのスクリプトを実行します。24 時間に 1 回を推奨します。"

#: views/admin/manage/scheduling.php:14
msgid "Execution Script URL"
msgstr "実行スクリプト URL"

#: views/admin/manage/scheduling.php:15
msgid "Run the execution script frequently. Once per two minutes is recommended."
msgstr "頻繁に実行スクリプトを実行します。2 分に一度はお勧めします。"

#: views/admin/manage/scheduling.php:17
msgid "Export File URL"
msgstr "エクスポートファイルのURL"

#: views/admin/manage/scheduling.php:20
msgid "Export Bundle URL"
msgstr "バンドルURLのエクスポート"

#: views/admin/manage/scheduling.php:25
msgid "Trigger Script"
msgstr "トリガースクリプト"

#: views/admin/manage/scheduling.php:27
msgid "Every time you want to schedule the export, run the trigger script."
msgstr "エクスポートをスケジュールするたびにトリガー スクリプトを実行します。"

#: views/admin/manage/scheduling.php:29
msgid "To schedule the export to run once every 24 hours, run the trigger script every 24 hours. Most hosts require you to use “wget” to access a URL. Ask your host for details."
msgstr "エクスポートを24時間に1回実行するようにスケジュールするには、24時間ごとにトリガースクリプトを実行します。 ほとんどのホストでは、URLにアクセスするために \"wget\"を使用する必要があります。 詳細はホストにお尋ねください。"

#: views/admin/manage/scheduling.php:31 views/admin/manage/scheduling.php:43
msgid "Example:"
msgstr "例:"

#: views/admin/manage/scheduling.php:35
msgid "Execution Script"
msgstr "実行スクリプト"

#: views/admin/manage/scheduling.php:37
msgid "The Execution script actually executes the export, once it has been triggered with the Trigger script."
msgstr "それがトリガー スクリプトを起動実行スクリプトは実際にエクスポート] を実行します。"

#: views/admin/manage/scheduling.php:39
msgid "It processes in iteration (only exporting a few records each time it runs) to optimize server load. It is recommended you run the execution script every 2 minutes."
msgstr "それプロセスの反復 (だけでいくつかのレコードのエクスポートの実行時間) サーバーの負荷を最適化します。2 分ごとの実行スクリプトを実行することをお勧めします。"

#: views/admin/manage/scheduling.php:41
msgid "It also operates this way in case of unexpected crashes by your web host. If it crashes before the export is finished, the next run of the cron job two minutes later will continue it where it left off, ensuring reliability."
msgstr "それはまたあなたの web ホストによって予期しないクラッシュの場合この方法を動作します。エクスポートが完了したら、cron ジョブの次回の実行 2 分前にクラッシュした場合後でそれを継続するところから、信頼性を確保します。"

#: views/admin/manage/scheduling.php:50
msgid "Your web host may require you to use a command other than wget, although wget is most common. In this case, you must asking your web hosting provider for help."
msgstr "あなたの web ホストは、wget は最も一般的な wget コマンド以外のコマンドを使用する必要があります。このケースでは、助けをあなたの web ホスティングプロバイダーを求めてする必要があります。"

#: views/admin/manage/templates.php:6
msgid "Download your import templates and use them to import your exported file to a separate WordPress/WP All Import installation."
msgstr "インポート テンプレートをダウンロードしてワードプレス/WP をすべてインポートは個別のインストールに、エクスポートしたファイルをインポートするのにはそれらを使用します。"

#: views/admin/manage/templates.php:10
msgid "Install these import templates in your separate WP All Import installation from the All Import -> Settings page by clicking the \"Import Templates\" button."
msgstr "これらのインポートすべてのインポート設定-> すべてインポートからインストール ページの「テンプレートのインポート」ボタンをクリックしてして、別 WP テンプレートをインストールします。"

#: views/admin/manage/update.php:93
msgid "Edit Template"
msgstr "テンプレートの編集"

#: views/admin/settings/index.php:17
msgid "Import/Export Templates"
msgstr "インポート/エクスポートのテンプレート"

#: views/admin/settings/index.php:31
msgid "Delete Selected"
msgstr "選択したものを削除"

#: views/admin/settings/index.php:32
msgid "Export Selected"
msgstr "エクスポート選択"

#: views/admin/settings/index.php:35
msgid "There are no templates saved"
msgstr "テンプレートは保存されてません"

#: views/admin/settings/index.php:40
msgid "Import Templates"
msgstr "テンプレートのインポート"

#: views/admin/settings/index.php:49
msgid "Cron Exports"
msgstr "Cron エクスポート"

#: views/admin/settings/index.php:54
msgid "Secret Key"
msgstr "シークレットキー"

#: views/admin/settings/index.php:57
msgid "Changing this will require you to re-create your existing cron jobs."
msgstr "この変更は既存の cron ジョブを再作成する上で必要になります。"

#: views/admin/settings/index.php:65
msgid "Files"
msgstr "ファイル"

#: views/admin/settings/index.php:70 views/admin/settings/index.php:73
msgid "Secure Mode"
msgstr "セキュアモード"

#: views/admin/settings/index.php:75
msgid "Randomize folder names"
msgstr "フォルダ名をランダム"

#: views/admin/settings/index.php:81
msgid "If enabled, exported files and temporary files will be saved in a folder with a randomized name in %s.<br/><br/>If disabled, exported files will be saved in the Media Library."
msgstr "有効にした場合は、%s で無作為に選ばれた名前を持つエクスポートされたファイルや一時ファイルをフォルダーに保存されます。<br><br>無効にした場合、エクスポートされたファイルがメディア ライブラリに保存されます。"

#: views/admin/settings/index.php:112
msgid "Zapier Integration"
msgstr "AWeber, Zapier を統合"

#: views/admin/settings/index.php:117
msgid "Getting Started"
msgstr "一般設定"

#: views/admin/settings/index.php:119
msgid "Zapier acts as a middle man between WP All Export and hundreds of other popular apps. To get started go to Zapier.com, create an account, and make a new Zap. Read more: <a target=\"_blank\" href=\"https://zapier.com/zapbook/wp-all-export-pro/\">https://zapier.com/zapbook/wp-all-export-pro/</a>"
msgstr "Zapier は、WP をすべてエクスポートと他の人気アプリの何百もの間中間の人として機能します。開始を取得する Zapier.com にアカウントを作成し、新しいザップを行きます。続きを読む: <a target=\"_blank\" href=\"https://zapier.com/zapbook/wp-all-export-pro/\">https://zapier.com/zapbook/wp-all-export-pro/</a>"

#: views/admin/settings/index.php:123
msgid "API Key"
msgstr "APIキー"

#: views/admin/settings/index.php:126
msgid "Generate New API Key"
msgstr "APIキーを生成"

#: views/admin/settings/index.php:127
msgid "Changing the key will require you to update your existing Zaps on Zapier."
msgstr "キーを変更する必要があります Zapier にあなたの既存の活力を更新します。"

#: views/admin/settings/index.php:110
msgid "Upgrade to the Pro edition of WP All Export for Zapier Integration"
msgstr "Zapier IntegrationのWP All ExportのPro版へのアップグレード"

#: views/admin/settings/index.php:134
msgid "Upgrade to the Pro edition of WP All Export to enable the Function Editor"
msgstr "WP All Export のプロ版にアップグレードし、ファンクションエディタを有効にする"

#: wp-all-export-pro.php:32
msgid "Please de-activate and remove the free version of the WP All Export before activating the paid version."
msgstr "非アクティブにしてくださいとアドオン有料版をアクティブにする前にWooCommerceの無料版を削除します。"

#: wp-all-export-pro.php:322 wp-all-export-pro.php:326
msgid "Uploads folder %s must be writable"
msgstr "アップロードフォルダ%sは書き込み可能でなければなりません"

#. Plugin URI of the plugin/theme
#: 
msgid "http://www.wpallimport.com/export/"
msgstr "http://www.wpallimport.com/export/"

#. Description of the plugin/theme
#: 
msgid "Export any post type to a CSV or XML file. Edit the exported data, and then re-import it later using WP All Import."
msgstr "任意のポストの種類を csv ファイルや XML ファイルにエクスポートします。エクスポートしたデータを編集し、WP のすべてのインポートを使用して後でそれを再インポートします。"

#. Author of the plugin/theme
#: 
msgid "Soflyy"
msgstr "Soflyy"

#: actions/init.php:19 actions/init.php:25
msgid "Error. Incorrect API key, check the WP All Export Pro settings page."
msgstr "エラーです。不適切な API キー、WP すべてエクスポート Pro の設定] ページを確認してください。"

#: actions/wp_ajax_get_xml_spec.php:34
msgid "Specification not found."
msgstr "仕様書が見つかりません。"

#: actions/wp_ajax_save_functions.php:43
msgid "PHP code must be wrapped in \"&lt;?php\" and \"?&gt;\""
msgstr "PHP コードを\"&lt;?php\" と \"?&gt;\" で囲む必要があります "

#: actions/wp_ajax_save_functions.php:52
msgid "File has been successfully updated."
msgstr "ファイルは正常に更新されました。"

#: actions/wp_loaded.php:168 actions/wp_loaded.php:224
msgid "Export #%s is currently in manually process. Request skipped."
msgstr "インポート#%sは現在手動で処理されています。 リクエストはスキップされました。"

#: actions/wp_loaded.php:181
msgid "#%s Cron job triggered."
msgstr "#%s のCronジョブがトリガーされました。"

#: actions/wp_loaded.php:188
msgid "Export #%s currently in process. Request skipped."
msgstr "現在プロセスで #%s をエクスポートします。要求はスキップされました。"

#: actions/wp_loaded.php:195
msgid "Export #%s already triggered. Request skipped."
msgstr "既にトリガー #%s をエクスポートします。要求はスキップされました。"

#: actions/wp_loaded.php:216
msgid "Export #%s is not triggered. Request skipped."
msgstr "#%s のエクスポートは実行されません。要求はスキップされました。"

#: actions/wp_loaded.php:276
msgid "Export #%s complete"
msgstr "完全な #%s をエクスポートします。"

#: actions/wp_loaded.php:283
msgid "Records Processed %s."
msgstr "%s で処理されたレコード。"

#: actions/wp_loaded.php:292
msgid "Export #%s already processing. Request skipped."
msgstr "既に処理 #%s をエクスポートします。要求はスキップされました。"

#: actions/wp_loaded.php:372
msgid "File doesn't exist"
msgstr "ファイルが存在しません。"

#: actions/wp_loaded.php:381
msgid "Export hash is not valid."
msgstr "エクスポートのハッシュが正しくありません。"

#: classes/updater.php:65
msgid "View WP All Export Pro Changelog"
msgstr "ビューWPすべてのエクスポートProの変更"

#: classes/updater.php:65
msgid "Changelog"
msgstr "変更履歴"

#: classes/updater.php:260
msgid "There is a new version of %1$s available. <a target=\"_blank\" class=\"thickbox\" href=\"%2$s\">View version %3$s details</a>."
msgstr "%1$s の利用可能な新しいバージョンがあります。<a target=\"_blank\" class=\"thickbox\" href=\"%2$s\"> %3$s バージョンの詳細を表示</a>"

#: classes/updater.php:267
msgid "There is a new version of %1$s available. <a target=\"_blank\" class=\"thickbox\" href=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\">update now</a>."
msgstr "%1$s の利用可能な新しいバージョンがあります。<a target=\"_blank\" class=\"thickbox\" href=\"%2$s\">%3$s バージョンの詳細を表示</a> か <a href=\"%4$s\">今すぐ更新</a>"

#: classes/updater.php:455
msgid "You do not have permission to install plugin updates"
msgstr "あなたはプラグインの更新プログラムをインストールする権限がありません"

#: classes/updater.php:455
msgid "Error"
msgstr "エラー  "

#: libraries/XmlExportWooCommerceOrder.php:1820
msgid "Order Line ID"
msgstr "注文ラインID"

#: views/admin/export/options/settings.php:19
msgid "If re-run, this export will only include records that have not been previously exported."
msgstr "再実行すると、このエクスポートには以前にエクスポートされていないレコードのみが含まれます。"

#: views/admin/export/options/settings.php:24
msgid "Only export %s that have been modified since last export"
msgstr "最後のエクスポートから変更されている %s をエクスポートのみ"

#: views/admin/export/options/settings.php:25
msgid "If re-run, this export will only include records that have been modified since last export run."
msgstr "かどうか再実行は、このエクスポートのみ、最後のエクスポートを実行以降に変更されたレコード。"

#: views/admin/export/process.php:91
msgid "Your server terminated the export process"
msgstr "あなたのサーバーは、エクスポート プロセスを終了しました。"

#: views/admin/export/process.php:92
msgid "Ask your host to check your server's error log. They will be able to determine why your server is terminating the export process."
msgstr "あなたのホスト サーバーのエラー ログを確認してくださいにお問い合わせください。なぜエクスポート プロセスを終了して、サーバーを決定できるようになります。"

#: views/admin/export/template.php:229 views/admin/export/template.php:301
#: views/admin/export/template.php:431
msgid "You will not be able to reimport data to the product variations, and you will not be able to import these products to another site."
msgstr "商品バリエーション データをインポートすることはできず、これらの製品を別のサイトにインポートすることはできません。"

#: views/admin/export/template.php:278
msgid "CSV Header Row"
msgstr "CSVヘッダー行"

#: views/admin/export/template.php:282
msgid "Include header row and column titles in export"
msgstr "輸出にヘッダー行と列のタイトルを含めます"

#: views/admin/export/template.php:284
msgid "Language"
msgstr "言語"

#: views/admin/export/template.php:319
msgid "Export Type"
msgstr "エクスポート形式"

#: views/admin/export/template.php:324
msgid "Choose your export type"
msgstr "エクスポートの種類を選択します。"

#: views/admin/export/template.php:330
msgid "Feed"
msgstr "フィード"

#: views/admin/export/template.php:361
msgid "Google Merchant Center Product Feed"
msgstr "Google Merchant Center 製品フィード"

#: views/admin/manage/google_merchants_info.php:2
msgid "Import Into Google Merchants Center"
msgstr "Google マーチャントセンターへのインポート"

#: views/admin/manage/google_merchants_info.php:6
msgid "Now that your export has been set up, you need to create a feed in Google Merchants Center and give it the URL of your export file from WP All Export."
msgstr "エクスポートに設定されている Google マーチャントセンターでフィードを作成し、WP すべてエクスポートから、エクスポート ファイルの URL を与える必要があります。"

#: views/admin/manage/index.php:172
msgid "Google Merchant Center Info"
msgstr "Google マーチャント センターの情報"

#: views/admin/settings/index.php:88
msgid "Licenses"
msgstr "ライセンス"

#: views/admin/settings/index.php:93
msgid "License Key"
msgstr "ライセンスキー"

#: views/admin/settings/index.php:99
msgid "Active"
msgstr "有効"

#: views/admin/settings/index.php:101
msgid "Activate License"
msgstr "ライセンスをアクティブ化"

#: views/admin/settings/index.php:106
msgid "A license key is required to access plugin updates. You can use your license key on an unlimited number of websites. Do not distribute your license key to 3rd parties. You can get your license key in the <a target=\"_blank\" href=\"http://www.wpallimport.com/portal\">customer portal</a>."
msgstr "ライセン スキーは、プラグインのアップデートにアクセスするために必要です。ウェブサイトの無制限のライセンス キーを使用できます。第三者へあなたのライセン スキーを配布できません。<a target=\"_blank\" href=\"http://www.wpallimport.com/portal\">カスタマー ポータル</a>であなたのライセン スキーを得ることができます。"

#. Plugin Name of the plugin/theme
#: 
msgid "WP All Export Pro"
msgstr "WP All Export"

