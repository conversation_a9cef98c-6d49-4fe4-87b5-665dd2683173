msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: WP All Export Pro\n"
"Language: fr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. Plugin Name of the plugin/theme
#: actions/admin_menu.php:11 actions/admin_menu.php:14
#: actions/admin_menu.php:15 actions/admin_menu.php:16
#: models/export/record.php:373 views/admin/export/index.php:9
#: views/admin/export/options.php:21 views/admin/export/process.php:15
#: views/admin/export/template.php:7 views/admin/manage/index.php:4
#: views/admin/manage/update.php:21 views/admin/settings/index.php:6
msgid "WP All Export"
msgstr "WP All Export"

#: actions/admin_menu.php:11
msgid "All Export"
msgstr ""

#: actions/admin_menu.php:14
msgid "Export to XML"
msgstr "Exporter vers XML"

#: actions/admin_menu.php:14
msgid "New Export"
msgstr "Nouvelle exportation"

#: actions/admin_menu.php:15 views/admin/export/process.php:115
#: views/admin/manage/index.php:5
msgid "Manage Exports"
msgstr "Gérer exportations"

#: actions/admin_menu.php:16 views/admin/settings/index.php:7
msgid "Settings"
msgstr "Paramètres"

#: views/admin/export/index.php:13 views/admin/export/options.php:25
#: views/admin/export/process.php:19 views/admin/export/template.php:12
#: views/admin/manage/update.php:25
msgid "Support"
msgstr "Assistance"

#: actions/wp_ajax_dismiss_export_warnings.php:6
#: actions/wp_ajax_dismiss_export_warnings.php:10
#: actions/wp_ajax_generate_zapier_api_key.php:6
#: actions/wp_ajax_generate_zapier_api_key.php:10
#: actions/wp_ajax_get_xml_spec.php:6 actions/wp_ajax_get_xml_spec.php:10
#: actions/wp_ajax_save_functions.php:6 actions/wp_ajax_save_functions.php:10
#: actions/wp_ajax_wpae_available_rules.php:6
#: actions/wp_ajax_wpae_available_rules.php:10
#: actions/wp_ajax_wpae_filtering.php:6 actions/wp_ajax_wpae_filtering.php:10
#: actions/wp_ajax_wpae_filtering_count.php:6
#: actions/wp_ajax_wpae_filtering_count.php:10
#: actions/wp_ajax_wpae_preview.php:8 actions/wp_ajax_wpae_preview.php:12
#: actions/wp_ajax_wpallexport.php:8 actions/wp_ajax_wpallexport.php:12
#: controllers/admin/manage.php:282 controllers/admin/manage.php:317
#: controllers/admin/manage.php:354 controllers/admin/manage.php:407
#: controllers/controller.php:119 wpae_api.php:7 wpae_api.php:11
msgid "Security check"
msgstr "Vérification de sécurité"

#: actions/wp_ajax_wpae_available_rules.php:21
#: views/admin/export/blocks/filters.php:18
msgid "Select Rule"
msgstr "Sélectionner une règle"

#: actions/wp_ajax_wpae_available_rules.php:27
#: views/admin/export/blocks/filters.php:54
#: views/admin/export/blocks/filters.php:68
msgid "In"
msgstr ""

#: actions/wp_ajax_wpae_available_rules.php:28
#: views/admin/export/blocks/filters.php:55
#: views/admin/export/blocks/filters.php:69
msgid "Not In"
msgstr ""

#: actions/wp_ajax_wpae_available_rules.php:38
#: actions/wp_ajax_wpae_available_rules.php:63
#: actions/wp_ajax_wpae_available_rules.php:74
#: actions/wp_ajax_wpae_available_rules.php:87
#: views/admin/export/blocks/filters.php:44
#: views/admin/export/blocks/filters.php:58
msgid "equals"
msgstr "est égal à"

#: actions/wp_ajax_wpae_available_rules.php:39
#: actions/wp_ajax_wpae_available_rules.php:64
#: actions/wp_ajax_wpae_available_rules.php:75
#: actions/wp_ajax_wpae_available_rules.php:88
#: views/admin/export/blocks/filters.php:45
#: views/admin/export/blocks/filters.php:59
msgid "doesn't equal"
msgstr "n'est pas égal"

#: actions/wp_ajax_wpae_available_rules.php:40
#: views/admin/export/blocks/filters.php:60
msgid "newer than"
msgstr ""

#: actions/wp_ajax_wpae_available_rules.php:41
#: views/admin/export/blocks/filters.php:61
msgid "equal to or newer than"
msgstr ""

#: actions/wp_ajax_wpae_available_rules.php:42
#: views/admin/export/blocks/filters.php:62
msgid "older than"
msgstr ""

#: actions/wp_ajax_wpae_available_rules.php:43
#: views/admin/export/blocks/filters.php:63
msgid "equal to or older than"
msgstr ""

#: actions/wp_ajax_wpae_available_rules.php:45
#: actions/wp_ajax_wpae_available_rules.php:54
#: actions/wp_ajax_wpae_available_rules.php:65
#: actions/wp_ajax_wpae_available_rules.php:94
#: views/admin/export/blocks/filters.php:50
#: views/admin/export/blocks/filters.php:64
msgid "contains"
msgstr "contient"

#: actions/wp_ajax_wpae_available_rules.php:46
#: actions/wp_ajax_wpae_available_rules.php:55
#: actions/wp_ajax_wpae_available_rules.php:66
#: actions/wp_ajax_wpae_available_rules.php:95
#: views/admin/export/blocks/filters.php:51
#: views/admin/export/blocks/filters.php:65
msgid "doesn't contain"
msgstr "ne contient pas"

#: actions/wp_ajax_wpae_available_rules.php:47
#: actions/wp_ajax_wpae_available_rules.php:67
#: actions/wp_ajax_wpae_available_rules.php:80
#: actions/wp_ajax_wpae_available_rules.php:96
#: views/admin/export/blocks/filters.php:52
#: views/admin/export/blocks/filters.php:66
msgid "is empty"
msgstr "est vide"

#: actions/wp_ajax_wpae_available_rules.php:48
#: actions/wp_ajax_wpae_available_rules.php:68
#: actions/wp_ajax_wpae_available_rules.php:81
#: actions/wp_ajax_wpae_available_rules.php:97
#: views/admin/export/blocks/filters.php:53
#: views/admin/export/blocks/filters.php:67
msgid "is not empty"
msgstr "n'est pas vide"

#: actions/wp_ajax_wpae_available_rules.php:76
#: actions/wp_ajax_wpae_available_rules.php:89
#: views/admin/export/blocks/filters.php:46
msgid "greater than"
msgstr "supérieur à"

#: actions/wp_ajax_wpae_available_rules.php:77
#: actions/wp_ajax_wpae_available_rules.php:90
#: views/admin/export/blocks/filters.php:47
msgid "equal to or greater than"
msgstr "plus grand ou égal à"

#: actions/wp_ajax_wpae_available_rules.php:78
#: actions/wp_ajax_wpae_available_rules.php:91
#: views/admin/export/blocks/filters.php:48
msgid "less than"
msgstr "moins d'une"

#: actions/wp_ajax_wpae_available_rules.php:79
#: actions/wp_ajax_wpae_available_rules.php:92
#: views/admin/export/blocks/filters.php:49
msgid "equal to or less than"
msgstr "plus petit ou égal à"

#: actions/wp_ajax_wpae_filtering.php:35
msgid "Add Filtering Options"
msgstr "Options de filtrage"

#: actions/wp_ajax_wpae_filtering.php:53
msgid "Migrate %s"
msgstr ""

#: actions/wp_ajax_wpae_filtering.php:57 actions/wp_ajax_wpae_filtering.php:64
msgid "Customize Export File"
msgstr ""

#: actions/wp_ajax_wpae_filtering_count.php:206
msgid "Unable to Export"
msgstr ""

#: actions/wp_ajax_wpae_filtering_count.php:207
msgid "Exporting taxonomies requires WordPress 4.6 or greater"
msgstr ""

#: actions/wp_ajax_wpae_filtering_count.php:264
msgid "Your export is ready to run."
msgstr "Votre exportation est prête à être lancée."

#: actions/wp_ajax_wpae_filtering_count.php:265
msgid "WP All Export will export %d %s."
msgstr "WP All Export exportera %d %s."

#: actions/wp_ajax_wpae_filtering_count.php:268
#: actions/wp_ajax_wpae_filtering_count.php:271
#: actions/wp_ajax_wpae_filtering_count.php:274
#: actions/wp_ajax_wpae_filtering_count.php:290
#: actions/wp_ajax_wpae_filtering_count.php:293
#: actions/wp_ajax_wpae_filtering_count.php:296
msgid "Nothing to export."
msgstr ""

#: actions/wp_ajax_wpae_filtering_count.php:269
#: actions/wp_ajax_wpae_filtering_count.php:291
msgid "All %s have already been exported."
msgstr ""

#: actions/wp_ajax_wpae_filtering_count.php:272
#: actions/wp_ajax_wpae_filtering_count.php:294
#: actions/wp_ajax_wpae_filtering_count.php:311
msgid "No matching %s found for selected filter rules."
msgstr ""

#: actions/wp_ajax_wpae_filtering_count.php:275
#: actions/wp_ajax_wpae_filtering_count.php:297
#: actions/wp_ajax_wpae_filtering_count.php:313
msgid "There aren't any %s to export."
msgstr ""

#: actions/wp_ajax_wpae_filtering_count.php:287
#: views/admin/export/template.php:27
msgid "Choose data to include in the export file."
msgstr "Choisissez données à inclure dans le fichier d'exportation."

#: actions/wp_ajax_wpae_filtering_count.php:309
msgid "Continue to configure and run your export."
msgstr ""

#: actions/wp_ajax_wpae_preview.php:51 controllers/admin/export.php:271
msgid "XML template is empty."
msgstr ""

#: actions/wp_ajax_wpae_preview.php:181 actions/wp_ajax_wpae_preview.php:337
msgid "Invalid XML"
msgstr ""

#: actions/wp_ajax_wpae_preview.php:184 actions/wp_ajax_wpae_preview.php:340
msgid "Line"
msgstr ""

#: actions/wp_ajax_wpae_preview.php:185 actions/wp_ajax_wpae_preview.php:341
msgid "Column"
msgstr ""

#: actions/wp_ajax_wpae_preview.php:186 actions/wp_ajax_wpae_preview.php:342
msgid "Code"
msgstr ""

#: actions/wp_ajax_wpae_preview.php:245
msgid "There was a problem parsing the custom XML template"
msgstr ""

#: actions/wp_ajax_wpae_preview.php:316
msgid "Can't preview the document."
msgstr ""

#: actions/wp_ajax_wpae_preview.php:318 actions/wp_ajax_wpae_preview.php:358
msgid "You can continue export or try to use &lt;data&gt; tag as root element."
msgstr ""

#: actions/wp_ajax_wpae_preview.php:356
msgid "Can't preview the document. Root element is not detected."
msgstr ""

#: actions/wp_ajax_wpae_preview.php:408
msgid "Data not found."
msgstr "Données Exif introuvables"

#: actions/wp_ajax_wpae_preview.php:417
msgid "This format is not supported."
msgstr "Ce format vidéo n'est pas supporté."

#: actions/wp_ajax_wpallexport.php:29
msgid "Export is not defined."
msgstr ""

#: actions/wp_ajax_wpallexport.php:51 actions/wp_ajax_wpallexport.php:78
#: views/admin/export/index.php:135 views/admin/export/index.php:170
msgid "Upgrade to the Pro edition of WP All Export to Export Users"
msgstr ""

#: actions/wp_ajax_wpallexport.php:55 actions/wp_ajax_wpallexport.php:82
#: views/admin/export/index.php:143 views/admin/export/index.php:175
msgid "Upgrade to the Pro edition of WP All Export to Export Comments"
msgstr ""

#: controllers/admin/export.php:119
msgid "ZipArchive class is missing on your server.<br/>Please contact your web hosting provider and ask them to install and activate ZipArchive."
msgstr ""

#: controllers/admin/export.php:123
msgid "Required PHP components are missing.<br/><br/>WP All Export requires XMLReader, and XMLWriter PHP modules to be installed.<br/>These are standard features of PHP, and are necessary for WP All Export to write the files you are trying to export.<br/>Please contact your web hosting provider and ask them to install and activate the DOMDocument, XMLReader, and XMLWriter PHP modules."
msgstr ""

#: controllers/admin/export.php:212 src/App/Controller/ExportController.php:102
msgid "You've reached your max_input_vars limit of %d. Please contact your web host to increase it."
msgstr ""

#: controllers/admin/export.php:243
msgid "You haven't selected any columns for export."
msgstr "Vous avez sélectionné aucun colonnes pour l'exportation."

#: controllers/admin/export.php:247
msgid "CSV delimiter must be specified."
msgstr ""

#: controllers/admin/export.php:254
msgid "Main XML Tag is required."
msgstr ""

#: controllers/admin/export.php:259
msgid "Single Record XML Tag is required."
msgstr ""

#: controllers/admin/export.php:263
msgid "Main XML Tag equals to Single Record XML Tag."
msgstr ""

#: controllers/admin/export.php:319 controllers/admin/export.php:430
#: controllers/admin/manage.php:218
msgid "Options updated"
msgstr "Options mises à jour !"

#: controllers/admin/manage.php:56
msgid "&laquo;"
msgstr "&laquo;"

#: controllers/admin/manage.php:57
msgid "&raquo;"
msgstr "&raquo;"

#: controllers/admin/manage.php:148 views/admin/manage/index.php:298
msgid "Export canceled"
msgstr "Exportation annulée"

#: controllers/admin/manage.php:246
msgid "Export deleted"
msgstr "Exportation supprimée"

#: controllers/admin/manage.php:274
msgid "%d %s deleted"
msgstr "%d %s supprimés"

#: controllers/admin/manage.php:274 views/admin/manage/bulk.php:10
msgid "export"
msgid_plural "exports"
msgstr[0] "Exporter"
msgstr[1] ""

#: controllers/admin/manage.php:341
msgid "The exported bundle is missing and can't be downloaded. Please re-run your export to re-generate it."
msgstr ""

#: controllers/admin/manage.php:346
msgid "This export doesn't exist."
msgstr ""

#: controllers/admin/manage.php:448
msgid "File format not supported"
msgstr "Ce format de fichier n'est pas pris en charge."

#: controllers/admin/manage.php:454 controllers/admin/manage.php:459
msgid "The exported file is missing and can't be downloaded. Please re-run your export to re-generate it."
msgstr "Le fichier exporté est absent et ne peut pas être téléchargé. S'il vous plaît ré-exécuter votre exportation de re-générer."

#: controllers/admin/settings.php:28
msgid "Settings saved"
msgstr "Paramètres enregistrés !"

#: controllers/admin/settings.php:51
msgid "Unknown File extension. Only txt files are permitted"
msgstr ""

#: controllers/admin/settings.php:64
msgid "%d template imported"
msgid_plural "%d templates imported"
msgstr[0] ""
msgstr[1] ""

#: controllers/admin/settings.php:66
msgid "Wrong imported data format"
msgstr ""

#: controllers/admin/settings.php:68
msgid "File is empty or doesn't exests"
msgstr ""

#: controllers/admin/settings.php:71
msgid "Undefined entry!"
msgstr ""

#: controllers/admin/settings.php:73
msgid "Please select file."
msgstr ""

#: controllers/admin/settings.php:79
msgid "Templates must be selected"
msgstr ""

#: controllers/admin/settings.php:88
msgid "%d template deleted"
msgid_plural "%d templates deleted"
msgstr[0] ""
msgstr[1] ""

#: filters/wpallexport_custom_types.php:7
msgid "WooCommerce Products"
msgstr "WooCommerce Produits"

#: filters/wpallexport_custom_types.php:8
msgid "WooCommerce Orders"
msgstr ""

#: filters/wpallexport_custom_types.php:9
msgid "WooCommerce Coupons"
msgstr ""

#: filters/wpallexport_custom_types.php:26
msgid "WooCommerce Customers"
msgstr ""

#: helpers/pmxe_render_xml_element.php:44 helpers/pmxe_render_xml_text.php:10
msgid "<strong>%s</strong> %s more"
msgstr "<strong>%s</strong> %s plus"

#: helpers/pmxe_render_xml_element.php:44 helpers/pmxe_render_xml_text.php:10
msgid "element"
msgid_plural "elements"
msgstr[0] "élément"
msgstr[1] ""

#: helpers/pmxe_render_xml_text.php:16
msgid "more"
msgstr "plus"

#: helpers/wp_all_export_get_cpt_name.php:10 views/admin/export/index.php:72
msgid "Users"
msgstr "Utilisateurs"

#: helpers/wp_all_export_get_cpt_name.php:10
msgid "User"
msgstr "Utilisateur"

#: helpers/wp_all_export_get_cpt_name.php:14
msgid "Customers"
msgstr ""

#: helpers/wp_all_export_get_cpt_name.php:14
#: libraries/XmlExportWooCommerceOrder.php:1732
msgid "Customer"
msgstr "Client"

#: helpers/wp_all_export_get_cpt_name.php:18 views/admin/export/index.php:68
msgid "Comments"
msgstr ""

#: helpers/wp_all_export_get_cpt_name.php:18
msgid "Comment"
msgstr ""

#: helpers/wp_all_export_get_cpt_name.php:27
msgid "Taxonomy Terms"
msgstr ""

#: helpers/wp_all_export_get_cpt_name.php:27
msgid "Taxonomy Term"
msgstr ""

#: helpers/wp_all_export_get_cpt_name.php:47
msgid "Records"
msgstr "Les enregistrements"

#: helpers/wp_all_export_get_cpt_name.php:47
msgid "Record"
msgstr "Dossier"

#: libraries/WpaePhpInterpreterErrorHandler.php:22
msgid "An unknown error occured"
msgstr ""

#: libraries/WpaePhpInterpreterErrorHandler.php:24
#: libraries/WpaePhpInterpreterErrorHandler.php:28
msgid "PHP Error"
msgstr ""

#: libraries/WpaePhpInterpreterErrorHandler.php:28
msgid "You probably forgot to close a quote"
msgstr ""

#: libraries/XmlExportACF.php:1026 libraries/XmlExportACF.php:1118
#: libraries/XmlExportACF.php:1147
msgid "ACF"
msgstr "ACF"

#: libraries/XmlExportComment.php:160
msgid "Comment meta"
msgstr ""

#: libraries/XmlExportEngine.php:201
msgid "Standard"
msgstr "Standard"

#: libraries/XmlExportEngine.php:205
msgid "Media"
msgstr ""

#: libraries/XmlExportEngine.php:209
msgid "Images"
msgstr ""

#: libraries/XmlExportEngine.php:259
msgid "Attachments"
msgstr ""

#: libraries/XmlExportEngine.php:307 libraries/XmlExportWooCommerce.php:512
#: views/admin/export/index.php:64
msgid "Taxonomies"
msgstr "Taxonomies"

#: libraries/XmlExportEngine.php:311 libraries/XmlExportWooCommerce.php:516
#: libraries/XmlExportWooCommerceOrder.php:1770
msgid "Custom Fields"
msgstr "Champs personnalisés"

#: libraries/XmlExportEngine.php:315 libraries/XmlExportUser.php:230
#: libraries/XmlExportWooCommerce.php:368
#: libraries/XmlExportWooCommerceCoupon.php:176
#: libraries/XmlExportWooCommerceOrder.php:1774
msgid "Other"
msgstr "Other"

#: libraries/XmlExportEngine.php:322
msgid "Author"
msgstr "Auteur"

#: libraries/XmlExportEngine.php:435
msgid "WP Query field is required"
msgstr "WP champ Query est nécessaire"

#: libraries/XmlExportEngine.php:668 libraries/XmlExportEngine.php:714
#: libraries/XmlExportWooCommerceOrder.php:1480
#: libraries/XmlExportWooCommerceOrder.php:1518
msgid "All"
msgstr "Tous"

#: libraries/XmlExportEngine.php:823
msgid "User Role"
msgstr ""

#: libraries/XmlExportEngine.php:1016
#: libraries/XmlExportWooCommerceOrder.php:1620
msgid "SQL Query"
msgstr "Requête SQL"

#: libraries/XmlExportEngine.php:1052
msgid "Missing custom XML template header."
msgstr ""

#: libraries/XmlExportEngine.php:1057
msgid "Missing custom XML template post loop."
msgstr ""

#: libraries/XmlExportEngine.php:1062
msgid "Missing custom XML template footer."
msgstr ""

#: src/Pro/Filtering/FilteringFactory.php:46
msgid "Filtering Options"
msgstr ""

#: libraries/XmlExportTaxonomy.php:128
msgid "Term Meta"
msgstr ""

#: libraries/XmlExportUser.php:212 libraries/XmlExportUser.php:223
msgid "Address"
msgstr ""

#: libraries/XmlExportUser.php:321 libraries/XmlExportWooCommerceOrder.php:1867
msgid "Customer User ID"
msgstr "Client ID de l'utilisateur"

#: libraries/XmlExportWooCommerce.php:372
#: libraries/XmlExportWooCommerce.php:502
msgid "Product Data"
msgstr "Caractéristiques du produit"

#: libraries/XmlExportWooCommerce.php:376
#: libraries/XmlExportWooCommerce.php:520
msgid "Attributes"
msgstr ""

#: libraries/XmlExportWooCommerce.php:524
msgid "Advanced"
msgstr "Avancé"

#: libraries/XmlExportWooCommerceOrder.php:951
#: views/admin/export/template/add_new_field.php:21
msgid "Upgrade to the Pro edition of WP All Export to Export Order Data"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1480
msgid "Data"
msgstr "Données"

#: libraries/XmlExportWooCommerceOrder.php:1728
msgid "Order"
msgstr "Ordre"

#: libraries/XmlExportWooCommerceOrder.php:1736
msgid "Items"
msgstr "Articles"

#: libraries/XmlExportWooCommerceOrder.php:1741
msgid "Taxes & Shipping"
msgstr "Taxes & Livraison"

#: libraries/XmlExportWooCommerceOrder.php:1745
msgid "Fees & Discounts"
msgstr "Frais & Réductions"

#: libraries/XmlExportWooCommerceOrder.php:1749
#: views/admin/manage/scheduling.php:47
msgid "Notes"
msgstr "Notes"

#: libraries/XmlExportWooCommerceOrder.php:1751
msgid "Note Content"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1752
msgid "Note Date"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1753
msgid "Note Visibility"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1754
msgid "Note User Name"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1755
msgid "Note User Email"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1759
msgid "Refunds"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1761
msgid "Refund Total"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1762
msgid "Refund ID"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1763
msgid "Refund Amounts"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1764
msgid "Refund Reason"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1765
msgid "Refund Date"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1766
msgid "Refund Author Email"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1789
msgid "Order ID"
msgstr "Numéro de commande"

#: libraries/XmlExportWooCommerceOrder.php:1790
msgid "Order Key"
msgstr "Afin clé"

#: libraries/XmlExportWooCommerceOrder.php:1791
msgid "Order Date"
msgstr "Date de la Commande"

#: libraries/XmlExportWooCommerceOrder.php:1792
msgid "Completed Date"
msgstr "Date d'achèvement"

#: libraries/XmlExportWooCommerceOrder.php:1793
msgid "Title"
msgstr "Titre"

#: libraries/XmlExportWooCommerceOrder.php:1794
msgid "Order Status"
msgstr "État de la commande"

#: libraries/XmlExportWooCommerceOrder.php:1795
msgid "Order Currency"
msgstr "Afin devise"

#: libraries/XmlExportWooCommerceOrder.php:1796
msgid "Payment Method Title"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1797
msgid "Order Total"
msgstr "Total Commande"

#: libraries/XmlExportWooCommerceOrder.php:1810
#: views/admin/export/template/advanced_field_options.php:51
msgid "Product ID"
msgstr "ID Produit :"

#: libraries/XmlExportWooCommerceOrder.php:1811
msgid "SKU"
msgstr "Réference"

#: libraries/XmlExportWooCommerceOrder.php:1812
#: views/admin/export/template/advanced_field_options.php:52
msgid "Product Name"
msgstr "Nom du projet"

#: libraries/XmlExportWooCommerceOrder.php:1813
msgid "Product Variation Details"
msgstr "Produit Variation Détails"

#: libraries/XmlExportWooCommerceOrder.php:1814
msgid "Quantity"
msgstr "Quantité"

#: libraries/XmlExportWooCommerceOrder.php:1815
msgid "Item Cost"
msgstr "coût de l’article"

#: libraries/XmlExportWooCommerceOrder.php:1816
msgid "Item Total"
msgstr "Total des biens"

#: libraries/XmlExportWooCommerceOrder.php:1817
msgid "Item Tax"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1818
msgid "Item Tax Total"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1819
msgid "Item Tax Data"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1837
msgid "Rate Code (per tax)"
msgstr "Code de taux (par l'impôt)"

#: libraries/XmlExportWooCommerceOrder.php:1838
msgid "Rate Percentage (per tax)"
msgstr "Rate Pourcentage (par l'impôt)"

#: libraries/XmlExportWooCommerceOrder.php:1839
msgid "Amount (per tax)"
msgstr "Montant (par l'impôt)"

#: libraries/XmlExportWooCommerceOrder.php:1840
msgid "Total Tax Amount"
msgstr "Montant total"

#: libraries/XmlExportWooCommerceOrder.php:1841
msgid "Shipping Method"
msgstr "Méthode de livraison"

#: libraries/XmlExportWooCommerceOrder.php:1842
msgid "Shipping Cost"
msgstr "Frais de livraison"

#: libraries/XmlExportWooCommerceOrder.php:1843
msgid "Shipping Taxes"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1852
msgid "Discount Amount (per coupon)"
msgstr "Montant de la remise (par coupon)"

#: libraries/XmlExportWooCommerceOrder.php:1853
msgid "Coupons Used"
msgstr "Valeur des coupons utilisés"

#: libraries/XmlExportWooCommerceOrder.php:1854
msgid "Total Discount Amount"
msgstr "Montant total de Discount"

#: libraries/XmlExportWooCommerceOrder.php:1855
msgid "Fee Amount (per surcharge)"
msgstr "Montant des frais (par supplément)"

#: libraries/XmlExportWooCommerceOrder.php:1856
msgid "Total Fee Amount"
msgstr "Montant total des frais"

#: libraries/XmlExportWooCommerceOrder.php:1857
msgid "Fee Taxes"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1868
msgid "Customer Note"
msgstr "Note client"

#: libraries/XmlExportWooCommerceOrder.php:1923
msgid "Billing Email Address"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1924
msgid "Customer Account Email Address"
msgstr ""

#: models/export/record.php:443
msgid "The other two files in this zip are the export file containing all of your data and the import template for WP All Import. \n"
"\n"
"To import this data, create a new import with WP All Import and upload this zip file."
msgstr "Les deux autres fichiers dans ce zip sont le fichier d'exportation contenant toutes vos données et le modèle d'importation pour WP toutes les importations. Pour importer ces données, créer une nouvelle importation avec WP toutes les importations et télécharger ce fichier zip."

#: views/admin/export/blocks/filters.php:2
msgid "Upgrade to the Pro edition of WP All Export to Add Filters"
msgstr ""

#: views/admin/export/blocks/filters.php:3 views/admin/export/index.php:136
#: views/admin/export/index.php:140 views/admin/export/index.php:144
#: views/admin/export/index.php:148 views/admin/export/index.php:171
#: views/admin/export/index.php:176 views/admin/export/template.php:345
#: views/admin/export/template.php:471 views/admin/export/template.php:526
#: views/admin/export/template/add_new_field.php:22
#: views/admin/export/template/advanced_field_options.php:63
#: views/admin/manage/scheduling.php:7 views/admin/settings/index.php:111
#: views/admin/settings/index.php:135
msgid "If you already own it, remove the free edition and install the Pro edition."
msgstr ""

#: views/admin/export/blocks/filters.php:4
msgid "Element"
msgstr "Élément"

#: views/admin/export/blocks/filters.php:5
msgid "Rule"
msgstr "Règle"

#: views/admin/export/blocks/filters.php:6
msgid "Value"
msgstr "Valeur"

#: views/admin/export/blocks/filters.php:12
msgid "Select Element"
msgstr "Sélectionner un élément"

#: views/admin/export/blocks/filters.php:25
msgid "Add Rule"
msgstr "Ajouter une règle"

#: views/admin/export/blocks/filters.php:37
msgid "Date filters use natural language.<br>For example, to return records created in the last week: <i>date ▸ newer than ▸ last week</i>.<br>For all records created in 2016: <i>date ▸ older than ▸ 1/1/2017</i> AND <i>date ▸ newer than ▸ 12/31/2015</i>"
msgstr ""

#: views/admin/export/blocks/filters.php:38
msgid "No filtering options. Add filtering options to only export records matching some specified criteria."
msgstr "Aucune des options de filtrage. Ajouter des options de filtrage uniquement à l'exportation correspondant à certains critères spécifiés."

#: views/admin/export/blocks/filters.php:122
msgid "Variable product matching rules: "
msgstr "Variable règles produits correspondants:"

#: views/admin/export/blocks/filters.php:124
msgid "Strict"
msgstr "Strict"

#: views/admin/export/blocks/filters.php:125
msgid "Permissive"
msgstr "permissif"

#: views/admin/export/blocks/filters.php:127
msgid "Strict matching requires all variations to pass in order for the product to be exported. Permissive matching allows the product to be exported if any of the variations pass."
msgstr "Appariement strict exige que toutes les variations de passer pour que le produit à exporter. Appariement permissive permet au produit d'être exporté si l'une des variations passe."

#: views/admin/export/index.php:10 views/admin/export/options.php:22
#: views/admin/export/process.php:16 views/admin/export/template.php:8
#: views/admin/manage/update.php:22
msgid "Export to XML / CSV"
msgstr "Exporter au format CSV"

#: views/admin/export/index.php:13 views/admin/export/options.php:25
#: views/admin/export/process.php:19 views/admin/export/template.php:15
#: views/admin/manage/update.php:25
msgid "Documentation"
msgstr "Documentation"

#: views/admin/export/index.php:30
msgid "First, choose what to export."
msgstr "Tout d'abord, choisir ce que d'exporter."

#: views/admin/export/index.php:33
msgid "Specific Post Type"
msgstr "Type de donnée spécifique"

#: views/admin/export/index.php:37
msgid "WP_Query Results"
msgstr "Résultats WP_Query"

#: views/admin/export/index.php:92
msgid "Choose a post type..."
msgstr "Choisissez le type de post"

#: views/admin/export/index.php:127
msgid "Select taxonomy"
msgstr ""

#: views/admin/export/index.php:139
msgid "Upgrade to the Pro edition of WP All Export to Export Customers"
msgstr ""

#: views/admin/export/index.php:147
msgid "Upgrade to the Pro edition of WP All Export to Export Taxonomies"
msgstr ""

#: views/admin/export/index.php:141
msgid "Post Type Query"
msgstr "Type Contribution Query"

#: views/admin/export/index.php:142
msgid "User Query"
msgstr "Requêtes de l'utilisateur"

#: views/admin/export/index.php:147
msgid "Comment Query"
msgstr ""

#: views/admin/export/index.php:189 views/admin/export/options.php:108
#: views/admin/export/process.php:120 views/admin/export/template.php:552
#: views/admin/manage/index.php:366 views/admin/manage/scheduling.php:57
#: views/admin/manage/templates.php:19 views/admin/manage/update.php:104
#: views/admin/settings/index.php:167
msgid "Created by"
msgstr "Créé par"

#: views/admin/export/options.php:4 views/admin/export/options.php:55
#: views/admin/export/options.php:97 views/admin/manage/update.php:3
#: views/admin/manage/update.php:55 views/admin/manage/update.php:97
msgid "Confirm & Run Export"
msgstr "Confirmer et exécuter l'exportation"

#: views/admin/export/options.php:5 views/admin/export/options.php:101
#: views/admin/manage/update.php:4 views/admin/manage/update.php:95
msgid "Save Export Configuration"
msgstr "Sauvegarder la configuration d'exportation"

#: views/admin/export/options.php:95 views/admin/export/template.php:543
msgid "Back"
msgstr ""

#: views/admin/export/options.php:100 views/admin/export/template.php:540
msgid "Back to Manage Exports"
msgstr "Retour à Gérer exportations"

#: views/admin/export/options/settings.php:4
msgid "Configure Advanced Settings"
msgstr ""

#: views/admin/export/options/settings.php:12
msgid "In each iteration, process"
msgstr "Dans chaque itération, processus"

#: views/admin/export/options/settings.php:12
#: views/admin/export/options/settings.php:18
msgid "records"
msgstr "enregistrements"

#: views/admin/export/options/settings.php:13
msgid "WP All Export must be able to process this many records in less than your server's timeout settings. If your export fails before completion, to troubleshoot you should lower this number."
msgstr "WP All Export doit être en mesure de traiter ces nombreux enregistrements en moins les paramètres de délai d'attente de votre serveur. Si votre exportation échoue avant la fin, pour résoudre les problèmes que vous devriez réduire ce nombre."

#: views/admin/export/options/settings.php:18
msgid "Only export %s once"
msgstr ""

#: views/admin/export/options/settings.php:19
msgid "If re-run, this export will only include records that have not been previously exported.<br><br><strong>Upgrade to the Pro edition of WP All Export to use this option.</strong>"
msgstr ""

#: views/admin/export/options/settings.php:24
msgid "Include BOM in export file"
msgstr "Inclure nomenclature dans le fichier d'exportation"

#: views/admin/export/options/settings.php:25
msgid "The BOM will help some programs like Microsoft Excel read your export file if it includes non-English characters."
msgstr "La nomenclature aidera certains programmes comme Microsoft Excel lire votre fichier d'exportation si elle comporte des caractères non-anglais."

#: views/admin/export/options/settings.php:30
msgid "Create a new file each time export is run"
msgstr ""

#: views/admin/export/options/settings.php:31
msgid "If disabled, the export file will be overwritten every time this export run."
msgstr ""

#: views/admin/export/options/settings.php:36
msgid "Split large exports into multiple files"
msgstr ""

#: views/admin/export/options/settings.php:39
msgid "Limit export to"
msgstr ""

#: views/admin/export/options/settings.php:39
msgid "records per file"
msgstr ""

#: views/admin/export/options/settings.php:47
msgid "Friendly Name:"
msgstr "Nom convivial"

#: views/admin/export/options/settings.php:48
msgid "Save friendly name..."
msgstr "Enregistrer nom convivial …"

#: views/admin/export/process.php:28
msgid "Export <span id=\"status\">in Progress...</span>"
msgstr "Exporter <span id=\"status\">la progression...</span>"

#: views/admin/export/process.php:29
msgid "Exporting may take some time. Please do not close your browser or refresh the page until the process is complete."
msgstr "Exportatrice peut prendre un certain temps. S'il vous plaît ne pas fermer votre navigateur ou actualiser la page jusqu'à ce que le processus est terminé."

#: views/admin/export/process.php:36
msgid "Time Elapsed"
msgstr "Temps écoulé"

#: views/admin/export/process.php:38 views/admin/export/process.php:73
msgid "Exported"
msgstr "Exportation"

#: views/admin/export/process.php:72
msgid "Export %ss"
msgstr ""

#: views/admin/export/process.php:84
msgid "WP All Export successfully exported your data!"
msgstr "WP All Export a exporté avec succès vos données!"

#: views/admin/export/process.php:94
msgid "Download Data"
msgstr "Télécharger de nouvelles données…"

#: views/admin/export/process.php:101 views/admin/manage/index.php:152
msgid "Split %ss"
msgstr ""

#: views/admin/export/process.php:106 views/admin/manage/index.php:140
#: views/admin/manage/index.php:147
msgid "Bundle"
msgstr "Vrac"

#: views/admin/export/process.php:107
msgid "Settings & Data for WP All Import"
msgstr "Réglages et données pour WP Tous importation"

#: views/admin/export/template.php:68
msgid "Upgrade to the Pro edition of WP All Export to Select Product Variation Options"
msgstr ""

#: views/admin/export/template.php:147
msgid "Drag & drop data from \"Available Data\" on the right to include it in the export or click \"Add Field To Export\" below."
msgstr "Drag & drop de données \"données disponibles\" sur la droite de l'inclure dans l'exportation ou cliquez sur \"Ajouter un champ à exporter\" ci-dessous."

#: views/admin/export/template.php:172
msgid "Warning: without %s you won't be able to re-import this data back to this site using WP All Import."
msgstr ""

#: views/admin/export/template.php:189
msgid "Add Field"
msgstr ""

#: views/admin/export/template.php:191
msgid "Add All"
msgstr ""

#: views/admin/export/template.php:193
msgid "Clear All"
msgstr ""

#: views/admin/export/template.php:199 views/admin/export/template.php:404
msgid "Preview"
msgstr ""

#: views/admin/export/template.php:209 views/admin/export/template.php:267
#: views/admin/export/template.php:413
msgid "Advanced Options"
msgstr ""

#: views/admin/export/template.php:216
msgid "Root XML Element"
msgstr ""

#: views/admin/export/template.php:225
msgid "Single %s XML Element"
msgstr ""

#: views/admin/export/template.php:236 views/admin/export/template.php:421
msgid "There are certain characters that cannot be included in an XML file unless they are wrapped in CDATA tags.<br/><a target='_blank' href='%s'>Click here to read more about CDATA tags.</a>"
msgstr ""

#: views/admin/export/template.php:239 views/admin/export/template.php:428
msgid "Automatically wrap data in CDATA tags when it contains illegal characters"
msgstr ""

#: views/admin/export/template.php:243 views/admin/export/template.php:436
msgid "Always wrap data in CDATA tags"
msgstr ""

#: views/admin/export/template.php:247 views/admin/export/template.php:444
msgid "Never wrap data in CDATA tags"
msgstr ""

#: views/admin/export/template.php:249 views/admin/export/template.php:447
msgid "Warning: This may result in an invalid XML file"
msgstr ""

#: views/admin/export/template.php:274
msgid "Separator:"
msgstr ""

#: views/admin/export/template.php:296
msgid "Display each product in its own row"
msgstr "Afficher chaque produit dans sa propre ligne"

#: views/admin/export/template.php:297
msgid "If an order contains multiple products, each product will have its own row. If disabled, each product will have its own column."
msgstr ""

#: views/admin/export/template.php:301
#: views/admin/export/template/advanced_field_options.php:15
msgid "Fill in empty columns"
msgstr ""

#: views/admin/export/template.php:303
msgid "If enabled, each order item will appear as its own row with all order info filled in for every column. If disabled, order info will only display on one row with only the order item info displaying in additional rows."
msgstr ""

#: views/admin/export/template.php:309
msgid "Export File Type"
msgstr ""

#: views/admin/export/template.php:314
msgid "Choose your export file type"
msgstr ""

#: views/admin/export/template.php:326
msgid "Spreadsheet"
msgstr ""

#: views/admin/export/template.php:320
msgid "XML Feed"
msgstr ""

#: views/admin/export/template.php:345
msgid "CSV File"
msgstr ""

#: views/admin/export/template.php:346
msgid "Excel File (XLS)"
msgstr ""

#: views/admin/export/template.php:347
msgid "Excel File (XLSX)"
msgstr ""

#: views/admin/export/template.php:344
msgid "Upgrade to the Pro edition of WP All Export to Export to Excel"
msgstr ""

#: views/admin/export/template.php:356
msgid "Simple XML Feed"
msgstr ""

#: views/admin/export/template.php:357
msgid "Custom XML Feed"
msgstr ""

#: views/admin/export/template.php:387
msgid "XML Editor"
msgstr ""

#: views/admin/export/template.php:399
msgid "Help"
msgstr ""

#: views/admin/export/template.php:475
#: views/admin/export/template/advanced_field_options.php:73
#: views/admin/settings/index.php:150
msgid "Function Editor"
msgstr "Éditeur de fonctions"

#: views/admin/export/template.php:470
#: views/admin/export/template/advanced_field_options.php:62
msgid "Upgrade to the Pro edition of WP All Export to use Custom PHP Functions"
msgstr ""

#: views/admin/export/template.php:488
#: views/admin/export/template/advanced_field_options.php:82
#: views/admin/settings/index.php:157
msgid "Save Functions"
msgstr "Enregistrer Fonctions"

#: views/admin/export/template.php:490
#: views/admin/export/template/advanced_field_options.php:73
#: views/admin/settings/index.php:158
msgid "Add functions here for use during your export. You can access this file at %s"
msgstr "Ajouter des fonctions ici pour une utilisation pendant votre exportation. Vous pouvez accéder à ce fichier à %s"

#: views/admin/export/template.php:508
msgid "Save settings as a template"
msgstr ""

#: views/admin/export/template.php:511
msgid "Template name..."
msgstr ""

#: views/admin/export/template.php:516
msgid "Load Template..."
msgstr ""

#: views/admin/export/template.php:525
msgid "Upgrade to the Pro edition of WP All Export to Export Custom XML"
msgstr ""

#: views/admin/export/template.php:546
msgid "Continue"
msgstr ""

#: views/admin/export/template.php:562
msgid "Available Data"
msgstr "données disponibles"

#: views/admin/export/template.php:583
msgid "Add Field To Export"
msgstr "Ajouter un champ Pour exporter"

#: views/admin/export/template.php:584 views/admin/export/template.php:594
msgid "Edit Export Field"
msgstr "Modifier les champs d'exportation"

#: views/admin/export/template.php:593
msgid "Custom XML Feeds"
msgstr ""

#: views/admin/export/template/add_new_field.php:4
msgid "What field would you like to export?"
msgstr "Dans quel domaine souhaitez-vous exporter?"

#: views/admin/export/template/add_new_field.php:10
msgid "What would you like to name the column/element in your exported file?"
msgstr "Que souhaitez-vous nommer la colonne / élément dans votre fichier exporté?"

#: views/admin/export/template/add_new_field.php:21
#: views/admin/manage/index.php:52 views/admin/manage/index.php:155
#: views/admin/manage/index.php:355
msgid "Delete"
msgstr "Supprimer"

#: views/admin/export/template/add_new_field.php:22
msgid "Done"
msgstr "Terminé"

#: views/admin/export/template/add_new_field.php:23
msgid "Close"
msgstr "Fermer"

#: views/admin/export/template/advanced_field_options.php:4
msgid "%%ID%% will be replaced with the ID of the post being exported, example: SELECT meta_value FROM wp_postmeta WHERE post_id=%%ID%% AND meta_key='your_meta_key';"
msgstr "%% ID %%sera remplacé par l'ID du poste étant exportée, exemple: SELECT FROM wp_postmeta meta_value OÙ post_id = %% %% ID ET meta_key = 'your_meta_key’;"

#: views/admin/export/template/advanced_field_options.php:11
msgid "Display each repeater row in its own csv line"
msgstr ""

#: views/admin/export/template/advanced_field_options.php:16
msgid "If enabled, each repeater row will appear as its own csv line with all post info filled in for every column."
msgstr ""

#: views/admin/export/template/advanced_field_options.php:24
msgid "Export featured image"
msgstr ""

#: views/admin/export/template/advanced_field_options.php:29
msgid "Export attached images"
msgstr ""

#: views/admin/export/template/advanced_field_options.php:31
msgid "Separator"
msgstr ""

#: views/admin/export/template/advanced_field_options.php:39
msgid "UNIX timestamp - PHP time()"
msgstr "Horodatage UNIX - temps de PHP ()"

#: views/admin/export/template/advanced_field_options.php:40
msgid "Natural Language PHP date()"
msgstr "Natural Language PHP date ()"

#: views/admin/export/template/advanced_field_options.php:43
msgid "date() Format"
msgstr "Format de Date"

#: views/admin/export/template/advanced_field_options.php:50
#: views/admin/export/template/custom_xml_help.php:58
msgid "Product SKU"
msgstr ""

#: views/admin/export/template/advanced_field_options.php:58
msgid "Export the value returned by a PHP function"
msgstr "Export de la valeur retournée par une fonction PHP"

#: views/admin/export/template/advanced_field_options.php:59
msgid "The value of the field chosen for export will be passed to the PHP function."
msgstr "La valeur du champ choisi pour l'exportation sera passé à la fonction PHP."

#: views/admin/export/template/custom_xml_help.php:3
msgid "The custom XML editor makes it easy to create an XML file with the exact structure you need. The syntax is simple and straightforward, yet powerful enough to allow you to pass your data through custom PHP functions."
msgstr ""

#: views/admin/export/template/custom_xml_help.php:5
msgid "Custom XML Editor"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:8
msgid "The custom XML editor is a template for your custom XML feed. Everything between the <span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_comment\">&lt;!-- BEGIN LOOP --&gt;</span> and <span class=\"wp_all_export_code_comment\">&lt;!-- END LOOP --&gt;</span></span> tags will be repeated for each exported post."
msgstr ""

#: views/admin/export/template/custom_xml_help.php:9
msgid "You can drag and drop elements from Available Data on the right into the editor on the left. You can also manually enter data into the export template."
msgstr ""

#: views/admin/export/template/custom_xml_help.php:10
msgid "For example, to add the post title to your export, you can either drag the title element into the editor, or you can manually edit the export template in editor to add it like this: <span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_tag\">&lt;my_custom_title&gt;<span class=\"wp_all_export_code_text\">{Title}</span>&lt;/my_custom_title&gt;</span></span>"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:13
msgid "PHP Functions"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:16
msgid "To add a custom PHP function to your XML template wrap it in brackets: <span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_text\">[my_function({Content})]"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:17
msgid "You can also use native PHP functions: <span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_text\">[str_replace(\",\",\"\",{Price})]"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:18
msgid "Whatever your function returns will appear in your exported XML file. You can pass as many elements as you like to your function so that they can be combined and processed in any way."
msgstr ""

#: views/admin/export/template/custom_xml_help.php:21
msgid "Repeating Fields and Arrays"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:24
msgid "Some fields, like images, have multiple values per post. WP All Export turns these fields into indexed arrays. Whenever WP All Export encounters an indexed array in an XML element it will repeat that element once for every value in the array."
msgstr ""

#: views/admin/export/template/custom_xml_help.php:25
msgid "For example, let's assume a post as two images attached to it - image1.jpg and image2.jpg - and we want to have one XML element for every image URL. Here's what our XML template will look like:"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:33
msgid "And here's how our exported XML file will look:"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:41
msgid "WP All Export will do this with all indexed arrays that it comes across. So if you have a function that returns an indexed array, that XML element will be repeated for each value. Likewise, you can take a field like {Image URL} and turn it into a string, like this:"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:45
msgid "And you'll just get one XML element with all of the values, like this:"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:51
msgid "Example Template"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:55
msgid "Let's say we want to make an XML feed of our WooCommerce products with these requirements:"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:57
msgid "Site name below the header, before the <span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_tag\">&lt;products&gt;</span></span> element"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:59
msgid "Product Title"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:60
msgid "Product Price (processed via a PHP function so that they end in .99)"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:61
msgid "Product image URLs wrapped in an <span class=\"wp_all_export_code\"><span class=\"wp_all_export_code_tag\">&lt;images&gt;</span></span> element"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:63
msgid "Here's what our XML template will look like in the editor:"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:81
msgid "Then in the Function Editor we'd define my_price_function() like so:"
msgstr ""

#: views/admin/export/template/custom_xml_help.php:89
msgid "If we had two products, each with two images, here's what our XML file would look like:"
msgstr ""

#: views/admin/export/variation_options.php:7
msgid "Product Variations"
msgstr "Variations du Produit"

#: views/admin/export/variation_options.php:10
msgid "WooCommerce stores each product variation as a separate product in the database, along with a parent product to tie all of the variations together.<br/><br/>If the product title is 'T-Shirt', then the parent product will be titled 'T-Shirt', and in the database each size/color combination will be a separate product with a title like 'Variation #23 of T-Shirt'."
msgstr ""

#: views/admin/export/variation_options.php:16
msgid "Only export product variations"
msgstr ""

#: views/admin/export/variation_options.php:27
#: views/admin/export/variation_options.php:61
msgid "Product variations use the parent product title"
msgstr ""

#: views/admin/export/variation_options.php:38
#: views/admin/export/variation_options.php:72
msgid "Product variations use the default variation product title"
msgstr ""

#: views/admin/export/variation_options.php:50
msgid "Export product variations and their parent products"
msgstr ""

#: views/admin/export/variation_options.php:82
msgid "Only export parent products"
msgstr ""

#: views/admin/help/index.php:1
msgid "WP All Export Support"
msgstr "WP All Export soutien"

#: views/admin/help/index.php:13
msgid "Thank you for using WP All Export."
msgstr ""

#: views/admin/help/index.php:15
msgid "While we do our best to provide technical support to users of the free version, we must prioritize requests from Pro users. If you need help with WP All Export please submit a ticket through the support form."
msgstr ""

#: views/admin/help/index.php:17
msgid "Upgrade to the Pro edition of WP All Export for Premium Support"
msgstr ""

#: views/admin/manage/bulk.php:10
msgid "Are you sure you want to delete <strong>%s</strong> selected %s?"
msgstr "Voulez-vous vraiment supprimer <strong>%s</strong> sélectionné %s?"

#: views/admin/manage/delete.php:1
msgid "Delete Export"
msgstr "Supprimer Export"

#: views/admin/manage/delete.php:4
msgid "Are you sure you want to delete <strong>%s</strong> export?"
msgstr "Voulez-vous vraiment supprimer <strong>%s</strong> ?"

#: views/admin/manage/index.php:18 views/admin/manage/index.php:20
msgid "Search Exports"
msgstr "Rechercher exportations"

#: views/admin/manage/index.php:27
msgid "ID"
msgstr "ID"

#: views/admin/manage/index.php:28
msgid "Name"
msgstr "Nom"

#: views/admin/manage/index.php:30
msgid "Query"
msgstr "Requête"

#: views/admin/manage/index.php:32
msgid "Summary"
msgstr "Résumé"

#: views/admin/manage/index.php:34
msgid "Info & Options"
msgstr "Infos & options"

#: views/admin/manage/index.php:51 views/admin/manage/index.php:353
msgid "Bulk Actions"
msgstr "Actions groupées"

#: views/admin/manage/index.php:54 views/admin/manage/index.php:361
msgid "Apply"
msgstr "Appliquer"

#: views/admin/manage/index.php:60
msgid "Displaying %s&#8211;%s of %s"
msgstr "Visualiser %s&#8211;%s de %s"

#: views/admin/manage/index.php:104
msgid "No previous exports found."
msgstr "Aucune exportation précédents trouvés."

#: views/admin/manage/index.php:134
msgid "Edit Export"
msgstr ""

#: views/admin/manage/index.php:135
msgid "Export Settings"
msgstr ""

#: views/admin/manage/index.php:163 views/admin/manage/scheduling.php:2
msgid "Cron Scheduling"
msgstr "Cron Scheduling"

#: views/admin/manage/index.php:209
msgid "Import with WP All Import"
msgstr "Importation avec WP Tous importation"

#: views/admin/manage/index.php:215 views/admin/manage/templates.php:2
msgid "Download Import Templates"
msgstr "Télécharger Importer des modèles"

#: views/admin/manage/index.php:227
msgid "Post Types: "
msgstr "Types d’articles:"

#: views/admin/manage/index.php:244
msgid "Y/m/d g:i a"
msgstr "d/m/Y G:i:s"

#: views/admin/manage/index.php:254
msgid "triggered with cron"
msgstr "déclenché avec cron"

#: views/admin/manage/index.php:261 views/admin/manage/index.php:276
#: views/admin/manage/index.php:290
msgid "last activity %s ago"
msgstr "Dernière activité il ya %s"

#: views/admin/manage/index.php:268
msgid "currently processing with cron"
msgstr "en cours de traitement avec cron"

#: views/admin/manage/index.php:283
msgid "Export currently in progress"
msgstr "Exportation en cours"

#: views/admin/manage/index.php:297
msgid "Export Attempt at %s"
msgstr "Exporter Tentative %s"

#: views/admin/manage/index.php:301
msgid "Last run: %s"
msgstr "Dernière course: %s"

#: views/admin/manage/index.php:301
msgid "never"
msgstr "jamais"

#: views/admin/manage/index.php:302
msgid "%d Records Exported"
msgstr "%d enregistrements exportés"

#: views/admin/manage/index.php:304
msgid "Format: %s"
msgstr "Format d’article : %s"

#: views/admin/manage/index.php:310
msgid "settings edited since last run"
msgstr "paramètres modifiés depuis la dernière exécution"

#: views/admin/manage/index.php:322
msgid "Edit"
msgstr "Modifier"

#: views/admin/manage/index.php:323
msgid "Run Export"
msgstr "Exécuter l'exportation"

#: views/admin/manage/index.php:325
msgid "Cancel Cron"
msgstr "Annuler Cron"

#: views/admin/manage/index.php:327
msgid "Cancel"
msgstr "Annuler"

#: views/admin/manage/index.php:357
msgid "Restore"
msgstr "Restaurer"

#: views/admin/manage/index.php:358
msgid "Delete Permanently"
msgstr "Supprimer Définitivement"

#: views/admin/manage/scheduling.php:6
msgid "Upgrade to the Pro edition of WP All Export for Scheduled Exports"
msgstr ""

#: views/admin/manage/scheduling.php:6
msgid "To schedule an export, you must create two cron jobs in your web hosting control panel. One cron job will be used to run the Trigger script, the other to run the Execution script."
msgstr ""

#: views/admin/manage/scheduling.php:10
msgid "Trigger Script URL"
msgstr ""

#: views/admin/manage/scheduling.php:11
msgid "Run the trigger script when you want to update your export. Once per 24 hours is recommended."
msgstr ""

#: views/admin/manage/scheduling.php:14
msgid "Execution Script URL"
msgstr ""

#: views/admin/manage/scheduling.php:15
msgid "Run the execution script frequently. Once per two minutes is recommended."
msgstr ""

#: views/admin/manage/scheduling.php:17
msgid "Export File URL"
msgstr "URL du fichier d'exportation"

#: views/admin/manage/scheduling.php:20
msgid "Export Bundle URL"
msgstr ""

#: views/admin/manage/scheduling.php:25
msgid "Trigger Script"
msgstr "Trigger Script"

#: views/admin/manage/scheduling.php:27
msgid "Every time you want to schedule the export, run the trigger script."
msgstr ""

#: views/admin/manage/scheduling.php:29
msgid "To schedule the export to run once every 24 hours, run the trigger script every 24 hours. Most hosts require you to use “wget” to access a URL. Ask your host for details."
msgstr ""

#: views/admin/manage/scheduling.php:31 views/admin/manage/scheduling.php:43
msgid "Example:"
msgstr "Exemple :"

#: views/admin/manage/scheduling.php:35
msgid "Execution Script"
msgstr "Script d'exécution"

#: views/admin/manage/scheduling.php:37
msgid "The Execution script actually executes the export, once it has been triggered with the Trigger script."
msgstr ""

#: views/admin/manage/scheduling.php:39
msgid "It processes in iteration (only exporting a few records each time it runs) to optimize server load. It is recommended you run the execution script every 2 minutes."
msgstr ""

#: views/admin/manage/scheduling.php:41
msgid "It also operates this way in case of unexpected crashes by your web host. If it crashes before the export is finished, the next run of the cron job two minutes later will continue it where it left off, ensuring reliability."
msgstr ""

#: views/admin/manage/scheduling.php:50
msgid "Your web host may require you to use a command other than wget, although wget is most common. In this case, you must asking your web hosting provider for help."
msgstr "Votre hébergeur peut vous obliger à utiliser une commande autre que wget, bien que wget est la plus courante. Dans ce cas, vous devez demander à votre fournisseur d'hébergement Web à l'aide."

#: views/admin/manage/templates.php:6
msgid "Download your import templates and use them to import your exported file to a separate WordPress/WP All Import installation."
msgstr "Télécharger vos modèles d'importation et les utiliser pour importer votre fichier exporté à une installation / WP toutes les importations de WordPress séparée."

#: views/admin/manage/templates.php:10
msgid "Install these import templates in your separate WP All Import installation from the All Import -> Settings page by clicking the \"Import Templates\" button."
msgstr "Installez ces modèles d'importation dans votre installation WP Tous importation séparé du Tout Import -> Page des paramètres en cliquant sur le bouton \"Importer des modèles\"."

#: views/admin/manage/update.php:93
msgid "Edit Template"
msgstr "Modifier le modèle du slide"

#: views/admin/settings/index.php:17
msgid "Import/Export Templates"
msgstr ""

#: views/admin/settings/index.php:31
msgid "Delete Selected"
msgstr ""

#: views/admin/settings/index.php:32
msgid "Export Selected"
msgstr ""

#: views/admin/settings/index.php:35
msgid "There are no templates saved"
msgstr ""

#: views/admin/settings/index.php:40
msgid "Import Templates"
msgstr ""

#: views/admin/settings/index.php:49
msgid "Cron Exports"
msgstr "Exportations Cron"

#: views/admin/settings/index.php:54
msgid "Secret Key"
msgstr "Clé secrète"

#: views/admin/settings/index.php:57
msgid "Changing this will require you to re-create your existing cron jobs."
msgstr "Changer cela va vous obliger à recréer vos tâches cron existants."

#: views/admin/settings/index.php:65
msgid "Files"
msgstr "Les fichiers"

#: views/admin/settings/index.php:70 views/admin/settings/index.php:73
msgid "Secure Mode"
msgstr "Mode sécurisé"

#: views/admin/settings/index.php:75
msgid "Randomize folder names"
msgstr "Aléatoire noms de dossiers"

#: views/admin/settings/index.php:81
msgid "If enabled, exported files and temporary files will be saved in a folder with a randomized name in %s.<br/><br/>If disabled, exported files will be saved in the Media Library."
msgstr ""

#: views/admin/settings/index.php:112
msgid "Zapier Integration"
msgstr ""

#: views/admin/settings/index.php:117
msgid "Getting Started"
msgstr ""

#: views/admin/settings/index.php:119
msgid "Zapier acts as a middle man between WP All Export and hundreds of other popular apps. To get started go to Zapier.com, create an account, and make a new Zap. Read more: <a target=\"_blank\" href=\"https://zapier.com/zapbook/wp-all-export-pro/\">https://zapier.com/zapbook/wp-all-export-pro/</a>"
msgstr ""

#: views/admin/settings/index.php:123
msgid "API Key"
msgstr ""

#: views/admin/settings/index.php:126
msgid "Generate New API Key"
msgstr ""

#: views/admin/settings/index.php:127
msgid "Changing the key will require you to update your existing Zaps on Zapier."
msgstr ""

#: views/admin/settings/index.php:110
msgid "Upgrade to the Pro edition of WP All Export for Zapier Integration"
msgstr ""

#: views/admin/settings/index.php:134
msgid "Upgrade to the Pro edition of WP All Export to enable the Function Editor"
msgstr ""

#: wp-all-export-pro.php:32
msgid "Please de-activate and remove the free version of the WP All Export before activating the paid version."
msgstr "S'il vous plaît de désactiver et supprimer la version libre de la WP toute exportation avant d'activer la version payante."

#: wp-all-export-pro.php:322 wp-all-export-pro.php:326
msgid "Uploads folder %s must be writable"
msgstr "Dossier Uploads %s doit être accessible en écriture"

#. Plugin URI of the plugin/theme
#: 
msgid "http://www.wpallimport.com/export/"
msgstr ""

#. Description of the plugin/theme
#: 
msgid "Export any post type to a CSV or XML file. Edit the exported data, and then re-import it later using WP All Import."
msgstr ""

#. Author of the plugin/theme
#: 
msgid "Soflyy"
msgstr ""

#: actions/init.php:19 actions/init.php:25
msgid "Error. Incorrect API key, check the WP All Export Pro settings page."
msgstr ""

#: actions/wp_ajax_get_xml_spec.php:34
msgid "Specification not found."
msgstr ""

#: actions/wp_ajax_save_functions.php:43
msgid "PHP code must be wrapped in \"&lt;?php\" and \"?&gt;\""
msgstr ""

#: actions/wp_ajax_save_functions.php:52
msgid "File has been successfully updated."
msgstr ""

#: actions/wp_loaded.php:168 actions/wp_loaded.php:224
msgid "Export #%s is currently in manually process. Request skipped."
msgstr ""

#: actions/wp_loaded.php:181
msgid "#%s Cron job triggered."
msgstr ""

#: actions/wp_loaded.php:188
msgid "Export #%s currently in process. Request skipped."
msgstr ""

#: actions/wp_loaded.php:195
msgid "Export #%s already triggered. Request skipped."
msgstr ""

#: actions/wp_loaded.php:216
msgid "Export #%s is not triggered. Request skipped."
msgstr ""

#: actions/wp_loaded.php:276
msgid "Export #%s complete"
msgstr ""

#: actions/wp_loaded.php:283
msgid "Records Processed %s."
msgstr ""

#: actions/wp_loaded.php:292
msgid "Export #%s already processing. Request skipped."
msgstr ""

#: actions/wp_loaded.php:372
msgid "File doesn't exist"
msgstr ""

#: actions/wp_loaded.php:381
msgid "Export hash is not valid."
msgstr ""

#: classes/updater.php:65
msgid "View WP All Export Pro Changelog"
msgstr ""

#: classes/updater.php:65
msgid "Changelog"
msgstr ""

#: classes/updater.php:260
msgid "There is a new version of %1$s available. <a target=\"_blank\" class=\"thickbox\" href=\"%2$s\">View version %3$s details</a>."
msgstr ""

#: classes/updater.php:267
msgid "There is a new version of %1$s available. <a target=\"_blank\" class=\"thickbox\" href=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\">update now</a>."
msgstr ""

#: classes/updater.php:455
msgid "You do not have permission to install plugin updates"
msgstr ""

#: classes/updater.php:455
msgid "Error"
msgstr ""

#: libraries/XmlExportWooCommerceOrder.php:1820
msgid "Order Line ID"
msgstr ""

#: views/admin/export/options/settings.php:19
msgid "If re-run, this export will only include records that have not been previously exported."
msgstr ""

#: views/admin/export/options/settings.php:24
msgid "Only export %s that have been modified since last export"
msgstr ""

#: views/admin/export/options/settings.php:25
msgid "If re-run, this export will only include records that have been modified since last export run."
msgstr ""

#: views/admin/export/process.php:91
msgid "Your server terminated the export process"
msgstr ""

#: views/admin/export/process.php:92
msgid "Ask your host to check your server's error log. They will be able to determine why your server is terminating the export process."
msgstr ""

#: views/admin/export/template.php:229 views/admin/export/template.php:301
#: views/admin/export/template.php:431
msgid "You will not be able to reimport data to the product variations, and you will not be able to import these products to another site."
msgstr ""

#: views/admin/export/template.php:278
msgid "CSV Header Row"
msgstr ""

#: views/admin/export/template.php:282
msgid "Include header row and column titles in export"
msgstr ""

#: views/admin/export/template.php:284
msgid "Language"
msgstr ""

#: views/admin/export/template.php:319
msgid "Export Type"
msgstr ""

#: views/admin/export/template.php:324
msgid "Choose your export type"
msgstr ""

#: views/admin/export/template.php:330
msgid "Feed"
msgstr ""

#: views/admin/export/template.php:361
msgid "Google Merchant Center Product Feed"
msgstr ""

#: views/admin/manage/google_merchants_info.php:2
msgid "Import Into Google Merchants Center"
msgstr ""

#: views/admin/manage/google_merchants_info.php:6
msgid "Now that your export has been set up, you need to create a feed in Google Merchants Center and give it the URL of your export file from WP All Export."
msgstr ""

#: views/admin/manage/index.php:172
msgid "Google Merchant Center Info"
msgstr ""

#: views/admin/settings/index.php:88
msgid "Licenses"
msgstr ""

#: views/admin/settings/index.php:93
msgid "License Key"
msgstr ""

#: views/admin/settings/index.php:99
msgid "Active"
msgstr ""

#: views/admin/settings/index.php:101
msgid "Activate License"
msgstr ""

#: views/admin/settings/index.php:106
msgid "A license key is required to access plugin updates. You can use your license key on an unlimited number of websites. Do not distribute your license key to 3rd parties. You can get your license key in the <a target=\"_blank\" href=\"http://www.wpallimport.com/portal\">customer portal</a>."
msgstr ""

#. Plugin Name of the plugin/theme
#: 
msgid "WP All Export Pro"
msgstr ""

