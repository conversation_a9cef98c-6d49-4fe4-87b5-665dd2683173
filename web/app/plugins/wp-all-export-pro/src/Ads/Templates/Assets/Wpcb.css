.wpallexport-plugin .wpcb-ad-container{
    background-color: #122031;
    color: #FFF;
    font-family: 'Inter';
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24px;
    text-align: center;
    max-width: 600px;
    padding: 25px;
    border-radius: 5px;
    margin-top: 25px;
    position: relative;
}

.wpallexport-plugin .wpcb-ad-container .wpcb-ad-close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #859eff;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    line-height: 1;
    text-decoration: none;
    z-index: 10;
}

.wpallexport-plugin .wpcb-ad-container .wpcb-ad-close:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}


.wpallexport-plugin .wpcb-ad-container h1{
    max-width: 550px;
    font-size: 35px;
}

.wpallexport-plugin .wpcb-ad-container .wpcb-primary-header{
    color: #FFF;
}

.wpallexport-plugin .wpcb-ad-container .wpcb-description{
    font-size: 16px;
}

.wpallexport-plugin .wpcb-ad-container .wpcb-secondary-header{
    color: #859eff;
}

.wpallexport-plugin .wpcb-ad-container .wpcb-button-container{
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    gap: 25px;
}

.wpallexport-plugin .wpcb-ad-container .wpcb-button-container .wpcb-text-link {
    color: #FFF;
    font-weight: 500;
}

.wpallexport-plugin .wpcb-ad-container .wpcb-button-container .wpcb-button a{
    background-color: #859eff;
    padding: 6px 12px;
    padding-right: 6px;
    border: 2px solid #859eff;
    border-radius: 100vmax;
    color: #1d1d1d;
    text-decoration: none;
    font-weight: 500;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    cursor: pointer;
    text-align: left;
}

.wpallexport-plugin .wpcb-ad-container .wpcb-button-container .wpcb-button .wpcb-button__icon-wrapper {
    display: flex;
}

.wpallexport-plugin .wpcb-ad-container .wpcb-button .wpcb-button__icon-wrapper svg {
    width: 18px;
    padding: 6px;
    border-radius: 100vmax;
    fill: #FFF;
    background-color: #122031;
}