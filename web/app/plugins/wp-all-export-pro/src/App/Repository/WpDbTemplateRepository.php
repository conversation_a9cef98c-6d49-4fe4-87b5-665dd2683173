<?php

namespace Wpae\App\Repository;


class WpDbTemplateRepository implements TemplateRepository
{

    public function getTemplate($id)
    {
        // TODO: Implement getTemplate() method.
    }

    public function saveTemplate($template)
    {
        // TODO: Implement saveTemplate() method.
    }

    public function loadTemplate($id)
    {
        // TODO: Implement loadTemplate() method.
    }
}